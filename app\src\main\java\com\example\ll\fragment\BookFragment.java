package com.example.ll.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.ll.R;
import com.example.ll.activity.BookCategoryActivity;
import com.example.ll.activity.BookSearchActivity;
import com.example.ll.adapter.BookAdapter;
import com.example.ll.adapter.BookCategoryAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Book;
import com.example.ll.model.BookCategory;

import java.util.ArrayList;
import java.util.List;

public class BookFragment extends Fragment {

    private EditText etSearch;
    private ImageView ivCategory;
    private RecyclerView rvCategories, rvBooks;
    private SwipeRefreshLayout swipeRefresh;
    
    private LibraryDatabase database;
    private BookCategoryAdapter categoryAdapter;
    private BookAdapter bookAdapter;
    
    private List<Book> allBooks = new ArrayList<>();
    private List<Book> filteredBooks = new ArrayList<>();

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_book, container, false);
        
        initViews(view);
        initData();
        setListeners();
        loadData();
        
        return view;
    }

    private void initViews(View view) {
        etSearch = view.findViewById(R.id.et_search);
        ivCategory = view.findViewById(R.id.iv_category);
        rvCategories = view.findViewById(R.id.rv_categories);
        rvBooks = view.findViewById(R.id.rv_books);
        swipeRefresh = view.findViewById(R.id.swipe_refresh);
        
        // 设置布局管理器
        rvCategories.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        rvBooks.setLayoutManager(new LinearLayoutManager(getContext()));
    }

    private void initData() {
        database = LibraryDatabase.getInstance(getContext());
        
        categoryAdapter = new BookCategoryAdapter(getContext(), new ArrayList<>());
        rvCategories.setAdapter(categoryAdapter);
        
        bookAdapter = new BookAdapter(getContext(), filteredBooks, false);
        rvBooks.setAdapter(bookAdapter);
        
        // 设置分类选择监听
        categoryAdapter.setOnCategorySelectedListener(new BookCategoryAdapter.OnCategorySelectedListener() {
            @Override
            public void onCategorySelected(BookCategory category) {
                filterBooksByCategory(category.getCategoryId());
            }
            
            @Override
            public void onAllCategoriesSelected() {
                filterBooksByCategory(-1); // -1表示显示所有分类
            }
        });
    }

    private void setListeners() {
        // 搜索框点击跳转到搜索页面
        etSearch.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), BookSearchActivity.class);
            startActivity(intent);
        });

        etSearch.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                Intent intent = new Intent(getContext(), BookSearchActivity.class);
                startActivity(intent);
            }
        });

        // 分类按钮
        ivCategory.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), BookCategoryActivity.class);
            startActivity(intent);
        });

        // 下拉刷新
        swipeRefresh.setOnRefreshListener(this::loadData);
    }

    private void loadData() {
        swipeRefresh.setRefreshing(true);

        // 临时使用模拟数据，避免Room数据库问题
        // TODO: 后续恢复完整的数据库查询逻辑

        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // 模拟分类数据
                List<BookCategory> mockCategories = createMockCategories();
                categoryAdapter.updateCategories(mockCategories);

                // 模拟图书数据
                List<Book> mockBooks = createMockBooks();
                allBooks.clear();
                allBooks.addAll(mockBooks);

                filteredBooks.clear();
                filteredBooks.addAll(mockBooks);
                bookAdapter.notifyDataSetChanged();

                swipeRefresh.setRefreshing(false);
            });
        }
    }

    private List<BookCategory> createMockCategories() {
        List<BookCategory> categories = new ArrayList<>();

        BookCategory category1 = new BookCategory("计算机科学", "编程、算法、软件工程等");
        category1.setCategoryId(1);
        categories.add(category1);

        BookCategory category2 = new BookCategory("文学", "小说、诗歌、散文等");
        category2.setCategoryId(2);
        categories.add(category2);

        BookCategory category3 = new BookCategory("历史", "中外历史、传记等");
        category3.setCategoryId(3);
        categories.add(category3);

        BookCategory category4 = new BookCategory("科学", "物理、化学、生物等");
        category4.setCategoryId(4);
        categories.add(category4);

        return categories;
    }

    private List<Book> createMockBooks() {
        List<Book> books = new ArrayList<>();

        // Java编程思想
        Book book1 = new Book("Java编程思想", "Bruce Eckel", "9787111213826");
        book1.setBookId(1);
        book1.setCategoryId(1);
        book1.setPublisher("机械工业出版社");
        book1.setTotalCopies(5);
        book1.setAvailableCopies(3);
        books.add(book1);

        // Effective Java
        Book book2 = new Book("Effective Java", "Joshua Bloch", "9787111255833");
        book2.setBookId(2);
        book2.setCategoryId(1);
        book2.setPublisher("机械工业出版社");
        book2.setTotalCopies(3);
        book2.setAvailableCopies(2);
        books.add(book2);

        // Android开发艺术探索
        Book book3 = new Book("Android开发艺术探索", "任玉刚", "9787121269394");
        book3.setBookId(3);
        book3.setCategoryId(1);
        book3.setPublisher("电子工业出版社");
        book3.setTotalCopies(2);
        book3.setAvailableCopies(1);
        books.add(book3);

        // 红楼梦
        Book book4 = new Book("红楼梦", "曹雪芹", "9787020002207");
        book4.setBookId(4);
        book4.setCategoryId(2);
        book4.setPublisher("人民文学出版社");
        book4.setTotalCopies(8);
        book4.setAvailableCopies(5);
        books.add(book4);

        // 西游记
        Book book5 = new Book("西游记", "吴承恩", "9787020002214");
        book5.setBookId(5);
        book5.setCategoryId(2);
        book5.setPublisher("人民文学出版社");
        book5.setTotalCopies(6);
        book5.setAvailableCopies(4);
        books.add(book5);

        // 算法导论
        Book book6 = new Book("算法导论", "Thomas H. Cormen", "9787111407010");
        book6.setBookId(6);
        book6.setCategoryId(1);
        book6.setPublisher("机械工业出版社");
        book6.setTotalCopies(4);
        book6.setAvailableCopies(0);
        books.add(book6);

        return books;
    }

    private void filterBooks(String query) {
        filteredBooks.clear();
        
        if (query.isEmpty()) {
            filteredBooks.addAll(allBooks);
        } else {
            String lowerQuery = query.toLowerCase();
            for (Book book : allBooks) {
                if (book.getTitle().toLowerCase().contains(lowerQuery) ||
                    book.getAuthor().toLowerCase().contains(lowerQuery) ||
                    (book.getIsbn() != null && book.getIsbn().toLowerCase().contains(lowerQuery))) {
                    filteredBooks.add(book);
                }
            }
        }
        
        bookAdapter.notifyDataSetChanged();
    }

    private void filterBooksByCategory(int categoryId) {
        filteredBooks.clear();
        
        if (categoryId == -1) {
            // 显示所有图书
            filteredBooks.addAll(allBooks);
        } else {
            // 按分类筛选
            for (Book book : allBooks) {
                if (book.getCategoryId() == categoryId) {
                    filteredBooks.add(book);
                }
            }
        }
        
        bookAdapter.notifyDataSetChanged();
        
        // 清空搜索框
        etSearch.setText("");
    }

    @Override
    public void onResume() {
        super.onResume();
        loadData(); // 页面恢复时刷新数据
    }
}
