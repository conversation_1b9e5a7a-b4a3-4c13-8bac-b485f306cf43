package com.example.ll.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.ll.R;
import com.example.ll.activity.BookCategoryActivity;
import com.example.ll.adapter.BookAdapter;
import com.example.ll.adapter.BookCategoryAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Book;
import com.example.ll.model.BookCategory;

import java.util.ArrayList;
import java.util.List;

public class BookFragment extends Fragment {

    private EditText etSearch;
    private ImageView ivCategory;
    private RecyclerView rvCategories, rvBooks;
    private SwipeRefreshLayout swipeRefresh;
    
    private LibraryDatabase database;
    private BookCategoryAdapter categoryAdapter;
    private BookAdapter bookAdapter;
    
    private List<Book> allBooks = new ArrayList<>();
    private List<Book> filteredBooks = new ArrayList<>();

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_book, container, false);
        
        initViews(view);
        initData();
        setListeners();
        loadData();
        
        return view;
    }

    private void initViews(View view) {
        etSearch = view.findViewById(R.id.et_search);
        ivCategory = view.findViewById(R.id.iv_category);
        rvCategories = view.findViewById(R.id.rv_categories);
        rvBooks = view.findViewById(R.id.rv_books);
        swipeRefresh = view.findViewById(R.id.swipe_refresh);
        
        // 设置布局管理器
        rvCategories.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        rvBooks.setLayoutManager(new LinearLayoutManager(getContext()));
    }

    private void initData() {
        database = LibraryDatabase.getInstance(getContext());
        
        categoryAdapter = new BookCategoryAdapter(getContext(), new ArrayList<>());
        rvCategories.setAdapter(categoryAdapter);
        
        bookAdapter = new BookAdapter(getContext(), filteredBooks, false);
        rvBooks.setAdapter(bookAdapter);
        
        // 设置分类选择监听
        categoryAdapter.setOnCategorySelectedListener(new BookCategoryAdapter.OnCategorySelectedListener() {
            @Override
            public void onCategorySelected(BookCategory category) {
                filterBooksByCategory(category.getCategoryId());
            }
            
            @Override
            public void onAllCategoriesSelected() {
                filterBooksByCategory(-1); // -1表示显示所有分类
            }
        });
    }

    private void setListeners() {
        // 搜索功能
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterBooks(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
        
        // 分类按钮
        ivCategory.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), BookCategoryActivity.class);
            startActivity(intent);
        });
        
        // 下拉刷新
        swipeRefresh.setOnRefreshListener(this::loadData);
    }

    private void loadData() {
        swipeRefresh.setRefreshing(true);

        // 临时使用模拟数据，避免Room数据库问题
        // TODO: 后续恢复完整的数据库查询逻辑

        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // 模拟分类数据
                List<BookCategory> mockCategories = new ArrayList<>();
                categoryAdapter.updateCategories(mockCategories);

                // 模拟图书数据
                List<Book> mockBooks = new ArrayList<>();
                allBooks.clear();
                allBooks.addAll(mockBooks);

                filteredBooks.clear();
                filteredBooks.addAll(mockBooks);
                bookAdapter.notifyDataSetChanged();

                swipeRefresh.setRefreshing(false);
            });
        }
    }

    private void filterBooks(String query) {
        filteredBooks.clear();
        
        if (query.isEmpty()) {
            filteredBooks.addAll(allBooks);
        } else {
            String lowerQuery = query.toLowerCase();
            for (Book book : allBooks) {
                if (book.getTitle().toLowerCase().contains(lowerQuery) ||
                    book.getAuthor().toLowerCase().contains(lowerQuery) ||
                    (book.getIsbn() != null && book.getIsbn().toLowerCase().contains(lowerQuery))) {
                    filteredBooks.add(book);
                }
            }
        }
        
        bookAdapter.notifyDataSetChanged();
    }

    private void filterBooksByCategory(int categoryId) {
        filteredBooks.clear();
        
        if (categoryId == -1) {
            // 显示所有图书
            filteredBooks.addAll(allBooks);
        } else {
            // 按分类筛选
            for (Book book : allBooks) {
                if (book.getCategoryId() == categoryId) {
                    filteredBooks.add(book);
                }
            }
        }
        
        bookAdapter.notifyDataSetChanged();
        
        // 清空搜索框
        etSearch.setText("");
    }

    @Override
    public void onResume() {
        super.onResume();
        loadData(); // 页面恢复时刷新数据
    }
}
