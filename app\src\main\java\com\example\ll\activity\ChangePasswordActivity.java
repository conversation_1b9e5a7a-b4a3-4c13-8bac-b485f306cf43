package com.example.ll.activity;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.ll.R;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.User;
import com.example.ll.utils.PasswordUtils;
import com.example.ll.utils.SharedPreferencesManager;
import com.example.ll.utils.UserUtils;

public class ChangePasswordActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private EditText etCurrentPassword, etNewPassword, etConfirmPassword;
    private TextView tvPasswordStrength, tvPasswordSuggestion;
    private Button btnChangePassword;
    
    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private User currentUser;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_change_password);
        
        initViews();
        initData();
        setListeners();
        loadUserData();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        etCurrentPassword = findViewById(R.id.et_current_password);
        etNewPassword = findViewById(R.id.et_new_password);
        etConfirmPassword = findViewById(R.id.et_confirm_password);
        tvPasswordStrength = findViewById(R.id.tv_password_strength);
        tvPasswordSuggestion = findViewById(R.id.tv_password_suggestion);
        btnChangePassword = findViewById(R.id.btn_change_password);
        
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("修改密码");
        }
    }
    
    private void initData() {
        database = LibraryDatabase.getInstance(this);
        prefsManager = SharedPreferencesManager.getInstance(this);
    }
    
    private void setListeners() {
        toolbar.setNavigationOnClickListener(v -> finish());
        
        // 监听新密码输入，实时显示密码强度
        etNewPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}
            
            @Override
            public void afterTextChanged(Editable s) {
                updatePasswordStrength(s.toString());
            }
        });
        
        btnChangePassword.setOnClickListener(v -> changePassword());
    }
    
    private void loadUserData() {
        new Thread(() -> {
            try {
                int userId = prefsManager.getCurrentUserId();
                currentUser = database.userDao().getUserById(userId);
                
                if (currentUser == null) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "用户信息加载失败", Toast.LENGTH_SHORT).show();
                        finish();
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载用户信息失败", Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        }).start();
    }
    
    private void updatePasswordStrength(String password) {
        PasswordUtils.PasswordStrength strength = PasswordUtils.checkPasswordStrength(password);
        
        // 更新密码强度显示
        tvPasswordStrength.setText(PasswordUtils.getPasswordStrengthDescription(strength));
        tvPasswordStrength.setTextColor(getResources().getColor(
            PasswordUtils.getPasswordStrengthColor(strength), null));
        
        // 更新密码建议
        tvPasswordSuggestion.setText(PasswordUtils.getPasswordSuggestion(password));
        
        // 检查常见弱密码
        if (PasswordUtils.isCommonWeakPassword(password)) {
            tvPasswordSuggestion.setText("⚠️ 这是常见的弱密码，建议使用更复杂的密码");
            tvPasswordSuggestion.setTextColor(getResources().getColor(android.R.color.holo_red_light, null));
        }
    }
    
    private void changePassword() {
        String currentPassword = etCurrentPassword.getText().toString().trim();
        String newPassword = etNewPassword.getText().toString().trim();
        String confirmPassword = etConfirmPassword.getText().toString().trim();
        
        // 验证输入
        if (currentPassword.isEmpty()) {
            etCurrentPassword.setError("请输入当前密码");
            return;
        }
        
        if (newPassword.isEmpty()) {
            etNewPassword.setError("请输入新密码");
            return;
        }
        
        if (!PasswordUtils.isPasswordSecure(newPassword)) {
            etNewPassword.setError("密码强度不够，请设置更安全的密码");
            return;
        }
        
        if (!newPassword.equals(confirmPassword)) {
            etConfirmPassword.setError("两次输入的密码不一致");
            return;
        }
        
        if (currentPassword.equals(newPassword)) {
            etNewPassword.setError("新密码不能与当前密码相同");
            return;
        }
        
        btnChangePassword.setEnabled(false);
        btnChangePassword.setText("修改中...");
        
        // 在后台线程中修改密码
        new Thread(() -> {
            try {
                // 验证当前密码
                if (!UserUtils.verifyUserLogin(currentUser, currentPassword)) {
                    runOnUiThread(() -> {
                        etCurrentPassword.setError("当前密码错误");
                        btnChangePassword.setEnabled(true);
                        btnChangePassword.setText("修改密码");
                    });
                    return;
                }
                
                // 更新密码
                UserUtils.updateUserPassword(currentUser, newPassword);
                database.userDao().updateUser(currentUser);
                
                runOnUiThread(() -> {
                    Toast.makeText(this, "密码修改成功", Toast.LENGTH_SHORT).show();
                    finish();
                });
                
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "密码修改失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                    btnChangePassword.setEnabled(true);
                    btnChangePassword.setText("修改密码");
                });
            }
        }).start();
    }
}
