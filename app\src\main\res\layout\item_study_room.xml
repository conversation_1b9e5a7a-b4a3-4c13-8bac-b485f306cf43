<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_room"
    android:layout_width="160dp"
    android:layout_height="120dp"
    android:layout_marginEnd="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_room_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="自习室A"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/tv_room_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="自习室"
            android:textSize="12sp"
            android:textColor="@color/primary_color"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/tv_capacity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="容量：50人"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="8dp" />

        <TextView
            android:id="@+id/tv_location"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="图书馆一楼"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
