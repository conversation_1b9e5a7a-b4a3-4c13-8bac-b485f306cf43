package com.example.ll.activity;

import android.os.Bundle;
import android.view.MenuItem;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;

import com.example.ll.R;
import com.example.ll.fragment.BookFragment;
import com.example.ll.fragment.BorrowingFragment;
import com.example.ll.fragment.HomeFragment;
import com.example.ll.fragment.ProfileFragment;
import com.example.ll.fragment.SeatFragment;
import com.google.android.material.bottomnavigation.BottomNavigationView;

public class UserMainActivity extends AppCompatActivity {

    private BottomNavigationView bottomNavigation;
    private Fragment currentFragment;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_user_main);
        
        initViews();
        setListeners();
        
        // 默认显示首页
        if (savedInstanceState == null) {
            showFragment(new HomeFragment());
        }
    }

    private void initViews() {
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void setListeners() {
        bottomNavigation.setOnItemSelectedListener(new BottomNavigationView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                Fragment fragment = null;
                
                int itemId = item.getItemId();
                if (itemId == R.id.nav_home) {
                    fragment = new HomeFragment();
                } else if (itemId == R.id.nav_books) {
                    fragment = new BookFragment();
                } else if (itemId == R.id.nav_borrowing) {
                    fragment = new BorrowingFragment();
                } else if (itemId == R.id.nav_seat) {
                    fragment = new SeatFragment();
                } else if (itemId == R.id.nav_profile) {
                    fragment = new ProfileFragment();
                }
                
                if (fragment != null) {
                    showFragment(fragment);
                    return true;
                }
                return false;
            }
        });
    }

    private void showFragment(Fragment fragment) {
        if (currentFragment != fragment) {
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.fragment_container, fragment)
                    .commit();
            currentFragment = fragment;
        }
    }

    @Override
    public void onBackPressed() {
        // 如果当前不是首页，返回首页
        if (bottomNavigation.getSelectedItemId() != R.id.nav_home) {
            bottomNavigation.setSelectedItemId(R.id.nav_home);
        } else {
            super.onBackPressed();
        }
    }

    /**
     * 切换到指定的标签页
     * @param tabIndex 标签页索引 (0:首页, 1:借阅, 2:图书, 3:座位, 4:个人)
     */
    public void switchToTab(int tabIndex) {
        int[] menuIds = {
            R.id.nav_home,      // 0
            R.id.nav_borrowing, // 1
            R.id.nav_books,     // 2
            R.id.nav_seat,      // 3
            R.id.nav_profile    // 4
        };

        if (tabIndex >= 0 && tabIndex < menuIds.length) {
            bottomNavigation.setSelectedItemId(menuIds[tabIndex]);
        }
    }
}
