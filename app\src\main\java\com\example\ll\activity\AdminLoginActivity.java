package com.example.ll.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.ll.R;
import com.example.ll.utils.SharedPreferencesManager;

public class AdminLoginActivity extends AppCompatActivity {

    private EditText etUsername, etPassword;
    private CheckBox cbRememberPassword;
    private Button btnLogin;
    private TextView tvBackToUser;

    private SharedPreferencesManager prefsManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_admin_login);

        initViews();
        initData();
        setListeners();
    }

    private void initViews() {
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        cbRememberPassword = findViewById(R.id.cb_remember_password);
        btnLogin = findViewById(R.id.btn_login);
        tvBackToUser = findViewById(R.id.tv_back_to_user);
    }

    private void initData() {
        prefsManager = SharedPreferencesManager.getInstance(this);

        // 如果记住密码，自动填充
        if (prefsManager.isRememberPassword()) {
            etUsername.setText(prefsManager.getCurrentUsername());
            etPassword.setText(prefsManager.getSavedPassword());
            cbRememberPassword.setChecked(true);
        }
    }

    private void setListeners() {
        btnLogin.setOnClickListener(v -> performAdminLogin());

        tvBackToUser.setOnClickListener(v -> {
            Intent intent = new Intent(AdminLoginActivity.this, LoginActivity.class);
            startActivity(intent);
            finish();
        });
    }

    private void performAdminLogin() {
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();

        if (TextUtils.isEmpty(username)) {
            etUsername.setError("请输入管理员用户名");
            etUsername.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(password)) {
            etPassword.setError("请输入密码");
            etPassword.requestFocus();
            return;
        }

        // 管理员演示账号验证
        if ("superadmin".equals(username) && "admin123".equals(password)) {
            // 登录成功
            prefsManager.saveUserLogin(999, username, "超级管理员", true);

            // 保存密码记住状态
            prefsManager.savePasswordRemember(cbRememberPassword.isChecked(), password);

            Toast.makeText(this, "管理员登录成功，欢迎 " + username, Toast.LENGTH_SHORT).show();

            // 跳转到管理员主界面
            Intent intent = new Intent(AdminLoginActivity.this, AdminMainActivity.class);
            startActivity(intent);
            finish();
        } else {
            Toast.makeText(this, "管理员用户名或密码错误\n演示账号：superadmin/admin123", Toast.LENGTH_LONG).show();
        }
    }
}
