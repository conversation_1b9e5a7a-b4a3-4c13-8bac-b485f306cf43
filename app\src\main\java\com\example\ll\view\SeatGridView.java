package com.example.ll.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.example.ll.R;
import com.example.ll.model.Seat;
import com.example.ll.utils.SeatUtils;

import java.util.ArrayList;
import java.util.List;

public class SeatGridView extends View {
    
    private static final int SEAT_SIZE = 120; // 座位大小（dp转px后）
    private static final int SEAT_MARGIN = 20; // 座位间距
    private static final int COLS_PER_ROW = 5; // 每行座位数
    
    private List<Seat> seats = new ArrayList<>();
    private Seat selectedSeat;
    private OnSeatClickListener listener;
    
    private Paint availablePaint;
    private Paint occupiedPaint;
    private Paint selectedPaint;
    private Paint maintenancePaint;
    private Paint textPaint;
    private Paint borderPaint;
    
    private int seatSizePx;
    private int seatMarginPx;
    
    public interface OnSeatClickListener {
        void onSeatClick(Seat seat);
    }
    
    public SeatGridView(Context context) {
        super(context);
        init();
    }
    
    public SeatGridView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public SeatGridView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // 转换dp到px
        float density = getContext().getResources().getDisplayMetrics().density;
        seatSizePx = (int) (SEAT_SIZE * density);
        seatMarginPx = (int) (SEAT_MARGIN * density);
        
        // 初始化画笔
        availablePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        availablePaint.setColor(ContextCompat.getColor(getContext(), R.color.seat_available));
        availablePaint.setStyle(Paint.Style.FILL);
        
        occupiedPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        occupiedPaint.setColor(ContextCompat.getColor(getContext(), R.color.seat_occupied));
        occupiedPaint.setStyle(Paint.Style.FILL);
        
        selectedPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        selectedPaint.setColor(ContextCompat.getColor(getContext(), R.color.seat_selected));
        selectedPaint.setStyle(Paint.Style.FILL);
        
        maintenancePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        maintenancePaint.setColor(ContextCompat.getColor(getContext(), R.color.seat_maintenance));
        maintenancePaint.setStyle(Paint.Style.FILL);
        
        borderPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        borderPaint.setColor(Color.GRAY);
        borderPaint.setStyle(Paint.Style.STROKE);
        borderPaint.setStrokeWidth(2);
        
        textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        textPaint.setColor(Color.WHITE);
        textPaint.setTextSize(24 * getContext().getResources().getDisplayMetrics().density);
        textPaint.setTextAlign(Paint.Align.CENTER);
    }
    
    public void setSeats(List<Seat> seats) {
        this.seats = seats != null ? seats : new ArrayList<>();
        this.selectedSeat = null;
        invalidate();
        requestLayout();
    }
    
    public void setSelectedSeat(Seat seat) {
        this.selectedSeat = seat;
        invalidate();
    }
    
    public Seat getSelectedSeat() {
        return selectedSeat;
    }
    
    public void setOnSeatClickListener(OnSeatClickListener listener) {
        this.listener = listener;
    }
    
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int rows = (int) Math.ceil((double) seats.size() / COLS_PER_ROW);
        
        int width = COLS_PER_ROW * seatSizePx + (COLS_PER_ROW - 1) * seatMarginPx + getPaddingLeft() + getPaddingRight();
        int height = rows * seatSizePx + (rows - 1) * seatMarginPx + getPaddingTop() + getPaddingBottom();
        
        setMeasuredDimension(width, height);
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (seats.isEmpty()) {
            return;
        }
        
        int startX = getPaddingLeft();
        int startY = getPaddingTop();
        
        for (int i = 0; i < seats.size(); i++) {
            Seat seat = seats.get(i);
            
            int row = i / COLS_PER_ROW;
            int col = i % COLS_PER_ROW;
            
            int left = startX + col * (seatSizePx + seatMarginPx);
            int top = startY + row * (seatSizePx + seatMarginPx);
            int right = left + seatSizePx;
            int bottom = top + seatSizePx;
            
            RectF seatRect = new RectF(left, top, right, bottom);
            
            // 选择画笔颜色
            Paint paint = getSeatPaint(seat);
            
            // 绘制座位
            canvas.drawRoundRect(seatRect, 8, 8, paint);
            canvas.drawRoundRect(seatRect, 8, 8, borderPaint);
            
            // 绘制座位号
            String seatNumber = seat.getSeatNumber();
            float textX = seatRect.centerX();
            float textY = seatRect.centerY() + (textPaint.getTextSize() / 3);
            canvas.drawText(seatNumber, textX, textY, textPaint);
            
            // 绘制设施图标
            drawFacilityIcons(canvas, seat, seatRect);
        }
    }
    
    private Paint getSeatPaint(Seat seat) {
        if (seat.equals(selectedSeat)) {
            return selectedPaint;
        }
        
        switch (seat.getStatus()) {
            case SeatUtils.STATUS_AVAILABLE:
                return availablePaint;
            case SeatUtils.STATUS_OCCUPIED:
            case SeatUtils.STATUS_RESERVED:
                return occupiedPaint;
            case SeatUtils.STATUS_MAINTENANCE:
                return maintenancePaint;
            default:
                return availablePaint;
        }
    }
    
    private void drawFacilityIcons(Canvas canvas, Seat seat, RectF seatRect) {
        float iconSize = 16 * getContext().getResources().getDisplayMetrics().density;
        float iconMargin = 4 * getContext().getResources().getDisplayMetrics().density;
        
        float iconY = seatRect.bottom - iconSize - iconMargin;
        float iconX = seatRect.left + iconMargin;
        
        // 绘制电源图标
        if (seat.isHasPower()) {
            Paint iconPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            iconPaint.setColor(Color.YELLOW);
            canvas.drawCircle(iconX + iconSize/2, iconY + iconSize/2, iconSize/3, iconPaint);
            iconX += iconSize + iconMargin;
        }
        
        // 绘制网络图标
        if (seat.isHasNetwork()) {
            Paint iconPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            iconPaint.setColor(Color.CYAN);
            canvas.drawCircle(iconX + iconSize/2, iconY + iconSize/2, iconSize/3, iconPaint);
        }
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            float x = event.getX();
            float y = event.getY();
            
            Seat clickedSeat = getSeatAt(x, y);
            if (clickedSeat != null) {
                // 只有可用的座位才能被选择
                if (SeatUtils.isSeatAvailable(clickedSeat)) {
                    setSelectedSeat(clickedSeat);
                    if (listener != null) {
                        listener.onSeatClick(clickedSeat);
                    }
                }
                return true;
            }
        }
        
        return super.onTouchEvent(event);
    }
    
    private Seat getSeatAt(float x, float y) {
        int startX = getPaddingLeft();
        int startY = getPaddingTop();
        
        for (int i = 0; i < seats.size(); i++) {
            int row = i / COLS_PER_ROW;
            int col = i % COLS_PER_ROW;
            
            int left = startX + col * (seatSizePx + seatMarginPx);
            int top = startY + row * (seatSizePx + seatMarginPx);
            int right = left + seatSizePx;
            int bottom = top + seatSizePx;
            
            if (x >= left && x <= right && y >= top && y <= bottom) {
                return seats.get(i);
            }
        }
        
        return null;
    }
    
    /**
     * 清除选择
     */
    public void clearSelection() {
        setSelectedSeat(null);
    }
    
    /**
     * 更新座位状态
     */
    public void updateSeatStatus(int seatId, String status) {
        for (Seat seat : seats) {
            if (seat.getSeatId() == seatId) {
                seat.setStatus(status);
                invalidate();
                break;
            }
        }
    }
}
