package com.example.ll.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.ll.model.BookCategory;

import java.util.List;

@Dao
public interface BookCategoryDao {
    
    @Insert
    long insertCategory(BookCategory category);
    
    @Update
    void updateCategory(BookCategory category);
    
    @Delete
    void deleteCategory(BookCategory category);
    
    @Query("SELECT * FROM book_categories WHERE categoryId = :categoryId")
    BookCategory getCategoryById(int categoryId);
    
    @Query("SELECT * FROM book_categories ORDER BY sortOrder, categoryName")
    List<BookCategory> getAllCategories();
    
    @Query("SELECT * FROM book_categories WHERE parentId IS NULL ORDER BY sortOrder, categoryName")
    List<BookCategory> getRootCategories();
    
    @Query("SELECT * FROM book_categories WHERE parentId = :parentId ORDER BY sortOrder, categoryName")
    List<BookCategory> getSubCategories(int parentId);
    
    @Query("SELECT * FROM book_categories WHERE categoryName LIKE :name")
    List<BookCategory> searchCategories(String name);
    
    @Query("SELECT COUNT(*) FROM book_categories")
    int getCategoryCount();
}
