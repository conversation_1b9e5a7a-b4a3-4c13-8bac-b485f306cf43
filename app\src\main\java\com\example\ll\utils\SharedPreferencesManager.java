package com.example.ll.utils;

import android.content.Context;
import android.content.SharedPreferences;

public class SharedPreferencesManager {
    private static final String PREF_NAME = "LibraryPrefs";
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_IS_LOGGED_IN = "is_logged_in";
    private static final String KEY_IS_ADMIN = "is_admin";
    private static final String KEY_USER_NICKNAME = "user_nickname";
    private static final String KEY_REMEMBER_PASSWORD = "remember_password";
    private static final String KEY_SAVED_PASSWORD = "saved_password";
    
    private SharedPreferences sharedPreferences;
    private SharedPreferences.Editor editor;
    private static SharedPreferencesManager instance;
    
    private SharedPreferencesManager(Context context) {
        sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        editor = sharedPreferences.edit();
    }
    
    public static synchronized SharedPreferencesManager getInstance(Context context) {
        if (instance == null) {
            instance = new SharedPreferencesManager(context.getApplicationContext());
        }
        return instance;
    }
    
    // 保存用户登录信息
    public void saveUserLogin(int userId, String username, String nickname, boolean isAdmin) {
        editor.putInt(KEY_USER_ID, userId);
        editor.putString(KEY_USERNAME, username);
        editor.putString(KEY_USER_NICKNAME, nickname);
        editor.putBoolean(KEY_IS_LOGGED_IN, true);
        editor.putBoolean(KEY_IS_ADMIN, isAdmin);
        editor.apply();
    }
    
    // 清除用户登录信息
    public void clearUserLogin() {
        editor.remove(KEY_USER_ID);
        editor.remove(KEY_USERNAME);
        editor.remove(KEY_USER_NICKNAME);
        editor.remove(KEY_IS_LOGGED_IN);
        editor.remove(KEY_IS_ADMIN);
        editor.apply();
    }
    
    // 获取当前用户ID
    public int getCurrentUserId() {
        return sharedPreferences.getInt(KEY_USER_ID, -1);
    }
    
    // 获取当前用户名
    public String getCurrentUsername() {
        return sharedPreferences.getString(KEY_USERNAME, "");
    }
    
    // 获取当前用户昵称
    public String getCurrentUserNickname() {
        return sharedPreferences.getString(KEY_USER_NICKNAME, "");
    }
    
    // 检查是否已登录
    public boolean isLoggedIn() {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false);
    }
    
    // 检查是否是管理员
    public boolean isAdmin() {
        return sharedPreferences.getBoolean(KEY_IS_ADMIN, false);
    }
    
    // 保存密码记住状态
    public void savePasswordRemember(boolean remember, String password) {
        editor.putBoolean(KEY_REMEMBER_PASSWORD, remember);
        if (remember) {
            editor.putString(KEY_SAVED_PASSWORD, password);
        } else {
            editor.remove(KEY_SAVED_PASSWORD);
        }
        editor.apply();
    }
    
    // 获取是否记住密码
    public boolean isRememberPassword() {
        return sharedPreferences.getBoolean(KEY_REMEMBER_PASSWORD, false);
    }
    
    // 获取保存的密码
    public String getSavedPassword() {
        return sharedPreferences.getString(KEY_SAVED_PASSWORD, "");
    }
    
    // 保存设置
    public void saveSetting(String key, String value) {
        editor.putString(key, value);
        editor.apply();
    }
    
    public void saveSetting(String key, boolean value) {
        editor.putBoolean(key, value);
        editor.apply();
    }
    
    public void saveSetting(String key, int value) {
        editor.putInt(key, value);
        editor.apply();
    }
    
    // 获取设置
    public String getStringSetting(String key, String defaultValue) {
        return sharedPreferences.getString(key, defaultValue);
    }
    
    public boolean getBooleanSetting(String key, boolean defaultValue) {
        return sharedPreferences.getBoolean(key, defaultValue);
    }
    
    public int getIntSetting(String key, int defaultValue) {
        return sharedPreferences.getInt(key, defaultValue);
    }
}
