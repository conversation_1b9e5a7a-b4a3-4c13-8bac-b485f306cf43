<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="修改密码"
        app:titleTextColor="@color/white" />

    <!-- 内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 安全提示 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/warning_light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔒"
                        android:textSize="24sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="安全提示"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/warning_dark"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="为了账号安全，请设置包含大小写字母、数字和特殊字符的强密码"
                            android:textSize="12sp"
                            android:textColor="@color/warning_dark" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 密码修改表单 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="当前密码"
                        app:endIconMode="password_toggle">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_current_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="新密码"
                        app:endIconMode="password_toggle">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_new_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 密码强度显示 -->
                    <TextView
                        android:id="@+id/tv_password_strength"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="密码强度：未设置"
                        android:textSize="12sp"
                        android:textColor="@color/text_hint"
                        android:layout_marginBottom="8dp" />

                    <!-- 密码建议 -->
                    <TextView
                        android:id="@+id/tv_password_suggestion"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="请输入密码"
                        android:textSize="11sp"
                        android:textColor="@color/text_hint"
                        android:layout_marginBottom="16dp"
                        android:lineSpacingExtra="2dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="确认新密码"
                        app:endIconMode="password_toggle">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_confirm_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 密码安全建议 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="密码安全建议"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• 密码长度至少8位\n• 包含大写字母、小写字母、数字\n• 包含特殊字符 (@$!%*?&amp;)\n• 避免使用常见密码\n• 不要使用个人信息作为密码"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- 修改按钮 -->
    <Button
        android:id="@+id/btn_change_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="修改密码"
        android:textSize="16sp"
        android:textColor="@color/white"
        android:background="@color/primary_color"
        android:layout_margin="16dp"
        android:padding="16dp" />

</LinearLayout>
