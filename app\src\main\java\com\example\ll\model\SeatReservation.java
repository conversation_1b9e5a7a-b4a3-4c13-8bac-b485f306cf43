package com.example.ll.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "seat_reservations")
public class SeatReservation {
    @PrimaryKey(autoGenerate = true)
    private int reservationId;
    private int userId;
    private int seatId;
    private String reservationDate; // YYYY-MM-DD格式
    private String startTime; // HH:MM格式
    private String endTime; // HH:MM格式
    private String status; // active, completed, cancelled, no_show
    private long checkInTime;
    private long checkOutTime;
    private long createdAt;
    private long updatedAt;

    // 关联的座位和房间信息（不存储在数据库中）
    private transient Seat seat;
    private transient StudyRoom studyRoom;

    // 构造函数
    public SeatReservation() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.status = "active";
    }

    public SeatReservation(int userId, int seatId, String reservationDate, String startTime, String endTime) {
        this();
        this.userId = userId;
        this.seatId = seatId;
        this.reservationDate = reservationDate;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    // Getter和Setter方法
    public int getReservationId() {
        return reservationId;
    }

    public void setReservationId(int reservationId) {
        this.reservationId = reservationId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getSeatId() {
        return seatId;
    }

    public void setSeatId(int seatId) {
        this.seatId = seatId;
    }

    public String getReservationDate() {
        return reservationDate;
    }

    public void setReservationDate(String reservationDate) {
        this.reservationDate = reservationDate;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(long checkInTime) {
        this.checkInTime = checkInTime;
    }

    public long getCheckOutTime() {
        return checkOutTime;
    }

    public void setCheckOutTime(long checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Seat getSeat() {
        return seat;
    }

    public void setSeat(Seat seat) {
        this.seat = seat;
    }

    public StudyRoom getStudyRoom() {
        return studyRoom;
    }

    public void setStudyRoom(StudyRoom studyRoom) {
        this.studyRoom = studyRoom;
    }

    // 判断预约是否有效
    public boolean isActive() {
        return "active".equals(status);
    }

    // 判断是否可以签到
    public boolean canCheckIn() {
        return "active".equals(status) && checkInTime == 0;
    }

    // 判断是否可以签退
    public boolean canCheckOut() {
        return "active".equals(status) && checkInTime > 0 && checkOutTime == 0;
    }

    @Override
    public String toString() {
        return "SeatReservation{" +
                "reservationId=" + reservationId +
                ", seatId=" + seatId +
                ", reservationDate='" + reservationDate + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
