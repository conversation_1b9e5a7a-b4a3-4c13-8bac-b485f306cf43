package com.example.ll.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.ll.R;
import com.example.ll.activity.BookSearchActivity;
import com.example.ll.activity.SeatReservationActivity;
import com.example.ll.adapter.BookAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Book;
import com.example.ll.model.BookCategory;
import com.example.ll.model.Seat;
import com.example.ll.model.StudyRoom;
import com.example.ll.model.User;
import com.example.ll.utils.SharedPreferencesManager;

import java.util.ArrayList;
import java.util.List;

public class HomeFragment extends Fragment {

    private TextView tvWelcome, tvBookCount, tvBorrowingCount, tvSeatCount;
    private CardView cardBookSearch, cardSeatReservation, cardMyBorrowing, cardMessages;
    private RecyclerView rvLatestBooks;
    
    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private BookAdapter bookAdapter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_home, container, false);
        
        initViews(view);
        initData();
        setListeners();
        loadData();
        
        return view;
    }

    private void initViews(View view) {
        tvWelcome = view.findViewById(R.id.tv_welcome);
        tvBookCount = view.findViewById(R.id.tv_book_count);
        tvBorrowingCount = view.findViewById(R.id.tv_borrowing_count);
        tvSeatCount = view.findViewById(R.id.tv_seat_count);
        
        cardBookSearch = view.findViewById(R.id.card_book_search);
        cardSeatReservation = view.findViewById(R.id.card_seat_reservation);
        cardMyBorrowing = view.findViewById(R.id.card_my_borrowing);
        cardMessages = view.findViewById(R.id.card_messages);
        
        rvLatestBooks = view.findViewById(R.id.rv_latest_books);
        rvLatestBooks.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
    }

    private void initData() {
        database = LibraryDatabase.getInstance(getContext());
        prefsManager = SharedPreferencesManager.getInstance(getContext());
        
        bookAdapter = new BookAdapter(getContext(), new ArrayList<>());
        rvLatestBooks.setAdapter(bookAdapter);
        
        // 设置欢迎信息
        String nickname = prefsManager.getCurrentUserNickname();
        tvWelcome.setText("欢迎回来，" + nickname);
    }

    private void setListeners() {
        cardBookSearch.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), BookSearchActivity.class);
            startActivity(intent);
        });
        
        cardSeatReservation.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), SeatReservationActivity.class);
            startActivity(intent);
        });
        
        cardMyBorrowing.setOnClickListener(v -> {
            // 切换到借阅页面
            if (getActivity() instanceof com.example.ll.activity.UserMainActivity) {
                // 这里可以通过接口回调切换到借阅页面
            }
        });
        
        cardMessages.setOnClickListener(v -> {
            // 跳转到消息页面
        });
    }

    private void loadData() {
        new Thread(() -> {
            try {
                // 获取统计数据
                int initialTotalBooks = database.bookDao().getAvailableBookCount();
                final int currentBorrowings = database.borrowingRecordDao().getCurrentBorrowingCount(prefsManager.getCurrentUserId());
                final int availableSeats = database.seatDao().getAllAvailableSeats().size();

                // 获取最新图书
                List<Book> initialLatestBooks = database.bookDao().getLatestBooks(10);

                // 如果数据库中没有数据，初始化一些示例数据
                final int finalTotalBooks;
                final List<Book> finalLatestBooks;

                if (initialLatestBooks.isEmpty()) {
                    initializeSampleData();
                    finalLatestBooks = database.bookDao().getLatestBooks(10);
                    finalTotalBooks = database.bookDao().getAvailableBookCount();
                } else {
                    finalLatestBooks = initialLatestBooks;
                    finalTotalBooks = initialTotalBooks;
                }

                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        tvBookCount.setText(String.valueOf(finalTotalBooks));
                        tvBorrowingCount.setText(String.valueOf(currentBorrowings));
                        tvSeatCount.setText(String.valueOf(availableSeats));

                        bookAdapter.updateBooks(finalLatestBooks);
                    });
                }

            } catch (Exception e) {
                e.printStackTrace();
                // 如果数据库查询失败，使用模拟数据作为后备
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        tvBookCount.setText("1000");
                        tvBorrowingCount.setText("5");
                        tvSeatCount.setText("120");

                        final List<Book> mockBooks = createMockLatestBooks();
                        bookAdapter.updateBooks(mockBooks);
                    });
                }
            }
        }).start();
    }

    private List<Book> createMockLatestBooks() {
        List<Book> books = new ArrayList<>();

        // 最新上架的图书
        Book book1 = new Book("Spring Boot实战", "Craig Walls", "9787115416179");
        book1.setBookId(7);
        book1.setAvailableCopies(3);
        books.add(book1);

        Book book2 = new Book("深入理解Java虚拟机", "周志明", "9787111421900");
        book2.setBookId(8);
        book2.setAvailableCopies(2);
        books.add(book2);

        Book book3 = new Book("设计模式", "Gang of Four", "9787111075776");
        book3.setBookId(9);
        book3.setAvailableCopies(4);
        books.add(book3);

        Book book4 = new Book("三体", "刘慈欣", "9787536692930");
        book4.setBookId(10);
        book4.setAvailableCopies(6);
        books.add(book4);

        Book book5 = new Book("人类简史", "尤瓦尔·赫拉利", "9787508647357");
        book5.setBookId(11);
        book5.setAvailableCopies(3);
        books.add(book5);

        return books;
    }

    private void initializeSampleData() {
        try {
            // 初始化图书分类
            initializeBookCategories();

            // 初始化图书数据
            initializeBooks();

            // 初始化用户数据
            initializeUsers();

            // 初始化座位和房间数据
            initializeSeatsAndRooms();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initializeBookCategories() {
        List<BookCategory> categories = new ArrayList<>();

        BookCategory category1 = new BookCategory("计算机科学", "编程、算法、软件工程等");
        category1.setCategoryId(1);
        categories.add(category1);

        BookCategory category2 = new BookCategory("文学", "小说、诗歌、散文等");
        category2.setCategoryId(2);
        categories.add(category2);

        BookCategory category3 = new BookCategory("历史", "中外历史、传记等");
        category3.setCategoryId(3);
        categories.add(category3);

        BookCategory category4 = new BookCategory("科学", "物理、化学、生物等");
        category4.setCategoryId(4);
        categories.add(category4);

        for (BookCategory category : categories) {
            database.bookCategoryDao().insertCategory(category);
        }
    }

    private void initializeBooks() {
        List<Book> books = new ArrayList<>();

        // 计算机类图书
        Book book1 = new Book("Java编程思想", "Bruce Eckel", "9787111213826");
        book1.setCategoryId(1);
        book1.setPublisher("机械工业出版社");
        book1.setPublishDate("2007-06-01");
        book1.setDescription("《Java编程思想》是Java程序设计领域的经典之作，深入浅出地介绍了Java语言的核心概念和编程技巧。");
        book1.setLocation("A区-2楼-计算机类-001");
        book1.setTotalCopies(5);
        book1.setAvailableCopies(3);
        books.add(book1);

        Book book2 = new Book("Effective Java", "Joshua Bloch", "9787111255833");
        book2.setCategoryId(1);
        book2.setPublisher("机械工业出版社");
        book2.setPublishDate("2009-01-01");
        book2.setDescription("《Effective Java》是Java之父James Gosling强烈推荐的Java进阶读物，包含了78条极具实用价值的经验规则。");
        book2.setLocation("A区-2楼-计算机类-002");
        book2.setTotalCopies(3);
        book2.setAvailableCopies(2);
        books.add(book2);

        Book book3 = new Book("Android开发艺术探索", "任玉刚", "9787121269394");
        book3.setCategoryId(1);
        book3.setPublisher("电子工业出版社");
        book3.setPublishDate("2015-09-01");
        book3.setDescription("本书是Android开发进阶类书籍，采用理论、源码和实践相结合的方式来阐述高水准的Android应用开发要点。");
        book3.setLocation("A区-2楼-计算机类-003");
        book3.setTotalCopies(2);
        book3.setAvailableCopies(1);
        books.add(book3);

        // 文学类图书
        Book book4 = new Book("红楼梦", "曹雪芹", "9787020002207");
        book4.setCategoryId(2);
        book4.setPublisher("人民文学出版社");
        book4.setPublishDate("1996-12-01");
        book4.setDescription("《红楼梦》是中国古典四大名著之首，是一部具有世界影响力的人情小说作品，举世公认的中国古典小说巅峰之作。");
        book4.setLocation("B区-1楼-文学类-001");
        book4.setTotalCopies(8);
        book4.setAvailableCopies(5);
        books.add(book4);

        Book book5 = new Book("三体", "刘慈欣", "9787536692930");
        book5.setCategoryId(2);
        book5.setPublisher("重庆出版社");
        book5.setPublishDate("2006-05-01");
        book5.setDescription("《三体》是刘慈欣创作的系列长篇科幻小说，是中国科幻文学的里程碑之作，获得了雨果奖等多项国际大奖。");
        book5.setLocation("B区-1楼-文学类-002");
        book5.setTotalCopies(8);
        book5.setAvailableCopies(6);
        books.add(book5);

        for (Book book : books) {
            database.bookDao().insertBook(book);
        }
    }

    private void initializeUsers() {
        // 创建默认用户账号
        User defaultUser = new User("admin", "123456", "<EMAIL>", "13800138000");
        defaultUser.setNickname("管理员");
        defaultUser.setStatus("active");
        database.userDao().insertUser(defaultUser);

        User testUser = new User("user", "123456", "<EMAIL>", "13800138001");
        testUser.setNickname("测试用户");
        testUser.setStatus("active");
        database.userDao().insertUser(testUser);
    }

    private void initializeSeatsAndRooms() {
        // 创建学习室
        StudyRoom room1 = new StudyRoom("自习室A", 50, "图书馆1楼");
        room1.setRoomType("study_room");
        room1.setFacilities("空调、WiFi、插座");
        long roomId1 = database.studyRoomDao().insertRoom(room1);

        StudyRoom room2 = new StudyRoom("阅览室B", 30, "图书馆2楼");
        room2.setRoomType("reading_room");
        room2.setFacilities("安静环境、自然光");
        long roomId2 = database.studyRoomDao().insertRoom(room2);

        // 为每个房间创建座位
        for (int i = 1; i <= 25; i++) {
            Seat seat = new Seat((int)roomId1, "A" + String.format("%02d", i));
            seat.setSeatType("single");
            seat.setHasPower(true);
            seat.setHasNetwork(true);
            database.seatDao().insertSeat(seat);
        }

        for (int i = 1; i <= 15; i++) {
            Seat seat = new Seat((int)roomId2, "B" + String.format("%02d", i));
            seat.setSeatType("single");
            seat.setHasPower(false);
            seat.setHasNetwork(true);
            database.seatDao().insertSeat(seat);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        loadData(); // 页面恢复时刷新数据
    }
}
