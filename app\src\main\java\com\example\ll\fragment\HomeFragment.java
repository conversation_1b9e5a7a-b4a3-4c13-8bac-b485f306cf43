package com.example.ll.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.ll.R;
import com.example.ll.activity.BookSearchActivity;
import com.example.ll.activity.SeatReservationActivity;
import com.example.ll.adapter.BookAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Book;
import com.example.ll.utils.SharedPreferencesManager;

import java.util.ArrayList;
import java.util.List;

public class HomeFragment extends Fragment {

    private TextView tvWelcome, tvBookCount, tvBorrowingCount, tvSeatCount;
    private CardView cardBookSearch, cardSeatReservation, cardMyBorrowing, cardMessages;
    private RecyclerView rvLatestBooks;
    
    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private BookAdapter bookAdapter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_home, container, false);
        
        initViews(view);
        initData();
        setListeners();
        loadData();
        
        return view;
    }

    private void initViews(View view) {
        tvWelcome = view.findViewById(R.id.tv_welcome);
        tvBookCount = view.findViewById(R.id.tv_book_count);
        tvBorrowingCount = view.findViewById(R.id.tv_borrowing_count);
        tvSeatCount = view.findViewById(R.id.tv_seat_count);
        
        cardBookSearch = view.findViewById(R.id.card_book_search);
        cardSeatReservation = view.findViewById(R.id.card_seat_reservation);
        cardMyBorrowing = view.findViewById(R.id.card_my_borrowing);
        cardMessages = view.findViewById(R.id.card_messages);
        
        rvLatestBooks = view.findViewById(R.id.rv_latest_books);
        rvLatestBooks.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
    }

    private void initData() {
        database = LibraryDatabase.getInstance(getContext());
        prefsManager = SharedPreferencesManager.getInstance(getContext());
        
        bookAdapter = new BookAdapter(getContext(), new ArrayList<>());
        rvLatestBooks.setAdapter(bookAdapter);
        
        // 设置欢迎信息
        String nickname = prefsManager.getCurrentUserNickname();
        tvWelcome.setText("欢迎回来，" + nickname);
    }

    private void setListeners() {
        cardBookSearch.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), BookSearchActivity.class);
            startActivity(intent);
        });
        
        cardSeatReservation.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), SeatReservationActivity.class);
            startActivity(intent);
        });
        
        cardMyBorrowing.setOnClickListener(v -> {
            // 切换到借阅页面
            if (getActivity() instanceof com.example.ll.activity.UserMainActivity) {
                // 这里可以通过接口回调切换到借阅页面
            }
        });
        
        cardMessages.setOnClickListener(v -> {
            // 跳转到消息页面
        });
    }

    private void loadData() {
        // 临时使用模拟数据，避免Room数据库问题
        // TODO: 后续恢复完整的数据库查询逻辑

        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // 模拟统计数据
                tvBookCount.setText("1000");
                tvBorrowingCount.setText("5");
                tvSeatCount.setText("120");

                // 模拟最新图书数据
                List<Book> mockBooks = createMockLatestBooks();
                bookAdapter.updateBooks(mockBooks);
            });
        }
    }

    private List<Book> createMockLatestBooks() {
        List<Book> books = new ArrayList<>();

        // 最新上架的图书
        Book book1 = new Book("Spring Boot实战", "Craig Walls", "9787115416179");
        book1.setBookId(7);
        book1.setAvailableCopies(3);
        books.add(book1);

        Book book2 = new Book("深入理解Java虚拟机", "周志明", "9787111421900");
        book2.setBookId(8);
        book2.setAvailableCopies(2);
        books.add(book2);

        Book book3 = new Book("设计模式", "Gang of Four", "9787111075776");
        book3.setBookId(9);
        book3.setAvailableCopies(4);
        books.add(book3);

        Book book4 = new Book("三体", "刘慈欣", "9787536692930");
        book4.setBookId(10);
        book4.setAvailableCopies(6);
        books.add(book4);

        Book book5 = new Book("人类简史", "尤瓦尔·赫拉利", "9787508647357");
        book5.setBookId(11);
        book5.setAvailableCopies(3);
        books.add(book5);

        return books;
    }

    @Override
    public void onResume() {
        super.onResume();
        loadData(); // 页面恢复时刷新数据
    }
}
