package com.example.ll.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class DateUtils {
    
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String TIME_FORMAT = "HH:mm";
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DISPLAY_DATE_FORMAT = "MM月dd日";
    public static final String DISPLAY_DATETIME_FORMAT = "MM月dd日 HH:mm";
    
    // 获取当前日期字符串
    public static String getCurrentDate() {
        return formatDate(System.currentTimeMillis(), DATE_FORMAT);
    }
    
    // 获取当前时间字符串
    public static String getCurrentTime() {
        return formatDate(System.currentTimeMillis(), TIME_FORMAT);
    }
    
    // 获取当前日期时间字符串
    public static String getCurrentDateTime() {
        return formatDate(System.currentTimeMillis(), DATETIME_FORMAT);
    }
    
    // 格式化时间戳
    public static String formatDate(long timestamp, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }
    
    // 格式化显示日期
    public static String formatDisplayDate(long timestamp) {
        return formatDate(timestamp, DISPLAY_DATE_FORMAT);
    }
    
    // 格式化显示日期时间
    public static String formatDisplayDateTime(long timestamp) {
        return formatDate(timestamp, DISPLAY_DATETIME_FORMAT);
    }
    
    // 获取几天后的日期
    public static String getDateAfterDays(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return formatDate(calendar.getTimeInMillis(), DATE_FORMAT);
    }
    
    // 获取几天后的时间戳
    public static long getTimestampAfterDays(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTimeInMillis();
    }
    
    // 获取几小时后的时间戳
    public static long getTimestampAfterHours(int hours) {
        return System.currentTimeMillis() + (hours * 60 * 60 * 1000L);
    }
    
    // 计算两个日期之间的天数差
    public static int getDaysBetween(long startTime, long endTime) {
        return (int) ((endTime - startTime) / (24 * 60 * 60 * 1000));
    }
    
    // 判断是否是今天
    public static boolean isToday(long timestamp) {
        String today = getCurrentDate();
        String targetDate = formatDate(timestamp, DATE_FORMAT);
        return today.equals(targetDate);
    }
    
    // 判断是否是昨天
    public static boolean isYesterday(long timestamp) {
        String yesterday = getDateAfterDays(-1);
        String targetDate = formatDate(timestamp, DATE_FORMAT);
        return yesterday.equals(targetDate);
    }
    
    // 获取友好的时间显示
    public static String getFriendlyTime(long timestamp) {
        long now = System.currentTimeMillis();
        long diff = now - timestamp;
        
        if (diff < 60 * 1000) { // 1分钟内
            return "刚刚";
        } else if (diff < 60 * 60 * 1000) { // 1小时内
            return (diff / (60 * 1000)) + "分钟前";
        } else if (isToday(timestamp)) { // 今天
            return "今天 " + formatDate(timestamp, TIME_FORMAT);
        } else if (isYesterday(timestamp)) { // 昨天
            return "昨天 " + formatDate(timestamp, TIME_FORMAT);
        } else { // 更早
            return formatDisplayDateTime(timestamp);
        }
    }
    
    // 解析日期字符串为时间戳
    public static long parseDate(String dateStr, String pattern) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern, Locale.getDefault());
            Date date = sdf.parse(dateStr);
            return date != null ? date.getTime() : 0;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    // 获取日期时间的时间戳
    public static long getDateTimeTimestamp(String date, String time) {
        try {
            String dateTimeStr = date + " " + time + ":00";
            return parseDate(dateTimeStr, DATETIME_FORMAT);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    // 检查时间是否在范围内
    public static boolean isTimeInRange(String time, String startTime, String endTime) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT, Locale.getDefault());
            Date timeDate = sdf.parse(time);
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);

            return timeDate != null && startDate != null && endDate != null &&
                   timeDate.compareTo(startDate) >= 0 && timeDate.compareTo(endDate) <= 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 比较两个时间字符串
     * @param time1 第一个时间
     * @param time2 第二个时间
     * @return 1: time1 > time2, 0: time1 = time2, -1: time1 < time2
     */
    public static int compareTime(String time1, String time2) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT, Locale.getDefault());
            Date t1 = sdf.parse(time1);
            Date t2 = sdf.parse(time2);

            if (t1 == null || t2 == null) {
                return 0;
            }

            return t1.compareTo(t2);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 比较两个日期字符串
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 1: date1 > date2, 0: date1 = date2, -1: date1 < date2
     */
    public static int compareDate(String date1, String date2) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());
            Date d1 = sdf.parse(date1);
            Date d2 = sdf.parse(date2);

            if (d1 == null || d2 == null) {
                return 0;
            }

            return d1.compareTo(d2);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 获取两个时间之间的分钟数
     */
    public static int getMinutesBetween(String startTime, String endTime) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT, Locale.getDefault());
            Date start = sdf.parse(startTime);
            Date end = sdf.parse(endTime);

            if (start == null || end == null) {
                return 0;
            }

            long diff = end.getTime() - start.getTime();
            return (int) (diff / (1000 * 60));
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 检查两个时间段是否有重叠
     */
    public static boolean isTimeRangeOverlap(String start1, String end1, String start2, String end2) {
        return !(compareTime(end1, start2) <= 0 || compareTime(start1, end2) >= 0);
    }



    /**
     * 检查日期是否是今天
     */
    public static boolean isToday(String date) {
        return getCurrentDate().equals(date);
    }

    /**
     * 检查日期是否是明天
     */
    public static boolean isTomorrow(String date) {
        return getDateAfterDays(1).equals(date);
    }

    /**
     * 获取友好的日期显示
     */
    public static String getFriendlyDate(String date) {
        if (isToday(date)) {
            return "今天";
        } else if (isTomorrow(date)) {
            return "明天";
        } else {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());
                Date d = sdf.parse(date);
                if (d == null) return date;

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(d);

                String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
                String weekDay = weekDays[calendar.get(Calendar.DAY_OF_WEEK) - 1];

                return String.format("%d月%d日 %s",
                    calendar.get(Calendar.MONTH) + 1,
                    calendar.get(Calendar.DAY_OF_MONTH),
                    weekDay);
            } catch (Exception e) {
                return date;
            }
        }
    }
}
