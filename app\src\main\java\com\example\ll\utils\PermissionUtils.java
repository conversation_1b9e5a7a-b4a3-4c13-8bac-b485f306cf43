package com.example.ll.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

public class PermissionUtils {
    
    // 权限请求码
    public static final int REQUEST_POST_NOTIFICATIONS = 1001;
    public static final int REQUEST_CAMERA = 1002;
    public static final int REQUEST_STORAGE = 1003;
    
    /**
     * 检查通知权限
     */
    public static boolean hasNotificationPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) 
                == PackageManager.PERMISSION_GRANTED;
        }
        return true; // Android 13以下不需要此权限
    }
    
    /**
     * 请求通知权限
     */
    public static void requestNotificationPermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission(activity)) {
                ActivityCompat.requestPermissions(activity, 
                    new String[]{Manifest.permission.POST_NOTIFICATIONS}, 
                    REQUEST_POST_NOTIFICATIONS);
            }
        }
    }
    
    /**
     * 检查是否应该显示权限说明
     */
    public static boolean shouldShowNotificationPermissionRationale(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ActivityCompat.shouldShowRequestPermissionRationale(activity, 
                Manifest.permission.POST_NOTIFICATIONS);
        }
        return false;
    }
    
    /**
     * 检查设备是否有相机硬件
     */
    public static boolean hasCameraHardware(Context context) {
        return context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA_ANY);
    }

    /**
     * 检查设备是否有前置相机
     */
    public static boolean hasFrontCamera(Context context) {
        return context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA_FRONT);
    }

    /**
     * 检查设备是否有后置相机
     */
    public static boolean hasBackCamera(Context context) {
        return context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA);
    }

    /**
     * 检查相机权限
     */
    public static boolean hasCameraPermission(Context context) {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA)
            == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 检查相机是否可用（硬件+权限）
     */
    public static boolean isCameraAvailable(Context context) {
        return hasCameraHardware(context) && hasCameraPermission(context);
    }

    /**
     * 请求相机权限
     */
    public static void requestCameraPermission(Activity activity) {
        if (!hasCameraHardware(activity)) {
            // 设备没有相机硬件
            return;
        }

        if (!hasCameraPermission(activity)) {
            ActivityCompat.requestPermissions(activity,
                new String[]{Manifest.permission.CAMERA},
                REQUEST_CAMERA);
        }
    }
    
    /**
     * 检查存储权限
     */
    public static boolean hasStoragePermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用新的媒体权限
            return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_IMAGES) 
                == PackageManager.PERMISSION_GRANTED;
        } else {
            // Android 12及以下使用传统存储权限
            return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) 
                == PackageManager.PERMISSION_GRANTED;
        }
    }
    
    /**
     * 请求存储权限
     */
    public static void requestStoragePermission(Activity activity) {
        if (!hasStoragePermission(activity)) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                ActivityCompat.requestPermissions(activity, 
                    new String[]{Manifest.permission.READ_MEDIA_IMAGES}, 
                    REQUEST_STORAGE);
            } else {
                ActivityCompat.requestPermissions(activity, 
                    new String[]{Manifest.permission.READ_EXTERNAL_STORAGE}, 
                    REQUEST_STORAGE);
            }
        }
    }
    
    /**
     * 处理权限请求结果
     */
    public static boolean handlePermissionResult(int requestCode, String[] permissions, int[] grantResults) {
        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            switch (requestCode) {
                case REQUEST_POST_NOTIFICATIONS:
                case REQUEST_CAMERA:
                case REQUEST_STORAGE:
                    return true;
            }
        }
        return false;
    }
    
    /**
     * 获取权限名称
     */
    public static String getPermissionName(int requestCode) {
        switch (requestCode) {
            case REQUEST_POST_NOTIFICATIONS:
                return "通知权限";
            case REQUEST_CAMERA:
                return "相机权限";
            case REQUEST_STORAGE:
                return "存储权限";
            default:
                return "未知权限";
        }
    }
    
    /**
     * 获取权限说明
     */
    public static String getPermissionRationale(int requestCode) {
        switch (requestCode) {
            case REQUEST_POST_NOTIFICATIONS:
                return "应用需要通知权限来向您发送重要的提醒信息，如借阅到期、座位预约等。";
            case REQUEST_CAMERA:
                return "应用需要相机权限来拍摄头像。如果您的设备没有相机或不需要此功能，可以跳过此权限。";
            case REQUEST_STORAGE:
                return "应用需要存储权限来选择和保存图片文件。";
            default:
                return "应用需要此权限来正常运行。";
        }
    }

    /**
     * 获取相机功能说明
     */
    public static String getCameraFeatureDescription(Context context) {
        if (!hasCameraHardware(context)) {
            return "您的设备没有相机硬件，无法使用拍照功能。";
        } else if (!hasCameraPermission(context)) {
            return "需要相机权限才能使用拍照功能。";
        } else {
            return "相机功能可用。";
        }
    }
    
    /**
     * 跳转到应用设置页面
     */
    public static void openAppSettings(Context context) {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", context.getPackageName(), null);
        intent.setData(uri);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
    
    /**
     * 跳转到通知设置页面
     */
    public static void openNotificationSettings(Context context) {
        Intent intent = new Intent();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.getPackageName());
        } else {
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", context.getPackageName(), null);
            intent.setData(uri);
        }
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
    
    /**
     * 检查所有必要权限
     */
    public static boolean hasAllRequiredPermissions(Context context) {
        return hasNotificationPermission(context);
        // 可以根据需要添加其他权限检查
        // && hasCameraPermission(context) 
        // && hasStoragePermission(context);
    }
    
    /**
     * 请求所有必要权限
     */
    public static void requestAllRequiredPermissions(Activity activity) {
        // 优先请求通知权限
        if (!hasNotificationPermission(activity)) {
            requestNotificationPermission(activity);
        }
        // 可以根据需要添加其他权限请求
    }
}
