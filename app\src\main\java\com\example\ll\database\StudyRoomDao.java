package com.example.ll.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.ll.model.StudyRoom;

import java.util.List;

@Dao
public interface StudyRoomDao {
    
    @Insert
    long insertRoom(StudyRoom room);
    
    @Update
    void updateRoom(StudyRoom room);
    
    @Delete
    void deleteRoom(StudyRoom room);
    
    @Query("SELECT * FROM study_rooms WHERE roomId = :roomId")
    StudyRoom getRoomById(int roomId);
    
    @Query("SELECT * FROM study_rooms WHERE status = 'available' ORDER BY roomName")
    List<StudyRoom> getAllAvailableRooms();
    
    @Query("SELECT * FROM study_rooms ORDER BY roomName")
    List<StudyRoom> getAllRooms();
    
    @Query("SELECT * FROM study_rooms WHERE roomType = :roomType AND status = 'available' ORDER BY roomName")
    List<StudyRoom> getRoomsByType(String roomType);
    
    @Query("SELECT * FROM study_rooms WHERE roomName LIKE :name AND status = 'available'")
    List<StudyRoom> searchRooms(String name);
    
    @Query("UPDATE study_rooms SET status = :status WHERE roomId = :roomId")
    void updateRoomStatus(int roomId, String status);
    
    @Query("SELECT COUNT(*) FROM study_rooms")
    int getRoomCount();
    
    @Query("SELECT COUNT(*) FROM study_rooms WHERE status = 'available'")
    int getAvailableRoomCount();
}
