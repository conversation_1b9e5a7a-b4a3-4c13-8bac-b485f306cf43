package com.example.ll.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "seats")
public class Seat {
    @PrimaryKey(autoGenerate = true)
    private int seatId;
    private int roomId;
    private String seatNumber;
    private String seatType; // single, double, computer
    private boolean hasPower;
    private boolean hasNetwork;
    private String status; // available, occupied, maintenance
    private long createdAt;
    private long updatedAt;

    // 构造函数
    public Seat() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.status = "available";
        this.seatType = "single";
        this.hasPower = false;
        this.hasNetwork = false;
    }

    public Seat(int roomId, String seatNumber) {
        this();
        this.roomId = roomId;
        this.seatNumber = seatNumber;
    }

    // Getter和Setter方法
    public int getSeatId() {
        return seatId;
    }

    public void setSeatId(int seatId) {
        this.seatId = seatId;
    }

    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public String getSeatNumber() {
        return seatNumber;
    }

    public void setSeatNumber(String seatNumber) {
        this.seatNumber = seatNumber;
    }

    public String getSeatType() {
        return seatType;
    }

    public void setSeatType(String seatType) {
        this.seatType = seatType;
    }

    public boolean isHasPower() {
        return hasPower;
    }

    public void setHasPower(boolean hasPower) {
        this.hasPower = hasPower;
    }

    public boolean isHasNetwork() {
        return hasNetwork;
    }

    public void setHasNetwork(boolean hasNetwork) {
        this.hasNetwork = hasNetwork;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 判断座位是否可用
    public boolean isAvailable() {
        return "available".equals(status);
    }

    @Override
    public String toString() {
        return "Seat{" +
                "seatId=" + seatId +
                ", roomId=" + roomId +
                ", seatNumber='" + seatNumber + '\'' +
                ", seatType='" + seatType + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
