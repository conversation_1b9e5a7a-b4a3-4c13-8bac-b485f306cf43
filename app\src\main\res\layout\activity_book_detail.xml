<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 图书基本信息 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="0dp"
                app:cardElevation="4dp"
                android:layout_marginBottom="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <!-- 图书封面 -->
                    <ImageView
                        android:id="@+id/iv_book_cover"
                        android:layout_width="100dp"
                        android:layout_height="140dp"
                        android:src="@drawable/ic_book_cover"
                        android:scaleType="centerCrop"
                        android:background="@drawable/bg_book_cover"
                        android:layout_marginEnd="16dp" />

                    <!-- 图书信息 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="图书标题"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp"
                            android:maxLines="2"
                            android:ellipsize="end" />

                        <TextView
                            android:id="@+id/tv_author"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="作者：未知"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/tv_publisher"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="出版社：未知"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/tv_publish_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="出版日期：未知"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="8dp" />

                        <!-- 状态和数量 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/tv_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="可借阅"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/success_color"
                                android:background="@drawable/bg_status_normal"
                                android:paddingHorizontal="8dp"
                                android:paddingVertical="4dp"
                                android:layout_marginEnd="8dp" />

                            <TextView
                                android:id="@+id/tv_available_copies"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="可借：3/5"
                                android:textSize="12sp"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 详细信息 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp"
                android:layout_margin="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="详细信息"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_isbn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="ISBN：未知"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_category"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="分类：未知"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_location"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="位置：未知"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="12dp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider_color"
                        android:layout_marginVertical="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="内容简介"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="暂无简介"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- 底部操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/surface_color"
        android:elevation="8dp">

        <Button
            android:id="@+id/btn_reserve"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="预约"
            android:textSize="16sp"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_color"
            android:layout_marginEnd="8dp"
            android:enabled="false" />

        <Button
            android:id="@+id/btn_borrow"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="借阅"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/button_primary"
            android:textColor="@android:color/white"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
