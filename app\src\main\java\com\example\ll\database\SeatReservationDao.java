package com.example.ll.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.ll.model.SeatReservation;

import java.util.List;

@Dao
public interface SeatReservationDao {
    
    @Insert
    long insertReservation(SeatReservation reservation);
    
    @Update
    void updateReservation(SeatReservation reservation);
    
    @Delete
    void deleteReservation(SeatReservation reservation);
    
    @Query("SELECT * FROM seat_reservations WHERE reservationId = :reservationId")
    SeatReservation getReservationById(int reservationId);
    
    @Query("SELECT * FROM seat_reservations WHERE userId = :userId ORDER BY reservationDate DESC, startTime DESC")
    List<SeatReservation> getReservationsByUser(int userId);
    
    @Query("SELECT * FROM seat_reservations WHERE userId = :userId AND status = 'active' ORDER BY reservationDate, startTime")
    List<SeatReservation> getActiveReservationsByUser(int userId);
    
    @Query("SELECT * FROM seat_reservations WHERE seatId = :seatId AND reservationDate = :date ORDER BY startTime")
    List<SeatReservation> getReservationsBySeatAndDate(int seatId, String date);
    
    @Query("SELECT * FROM seat_reservations WHERE reservationDate = :date ORDER BY startTime")
    List<SeatReservation> getReservationsByDate(String date);
    
    @Query("SELECT * FROM seat_reservations WHERE status = 'active' ORDER BY reservationDate, startTime")
    List<SeatReservation> getAllActiveReservations();
    
    @Query("SELECT * FROM seat_reservations WHERE seatId = :seatId AND reservationDate = :date AND status = 'active' AND ((startTime <= :startTime AND endTime > :startTime) OR (startTime < :endTime AND endTime >= :endTime) OR (startTime >= :startTime AND endTime <= :endTime))")
    List<SeatReservation> getConflictingReservations(int seatId, String date, String startTime, String endTime);
    
    @Query("UPDATE seat_reservations SET status = :status WHERE reservationId = :reservationId")
    void updateReservationStatus(int reservationId, String status);
    
    @Query("UPDATE seat_reservations SET checkInTime = :checkInTime WHERE reservationId = :reservationId")
    void checkIn(int reservationId, long checkInTime);
    
    @Query("UPDATE seat_reservations SET checkOutTime = :checkOutTime WHERE reservationId = :reservationId")
    void checkOut(int reservationId, long checkOutTime);
    
    @Query("SELECT COUNT(*) FROM seat_reservations WHERE userId = :userId AND status = 'active'")
    int getActiveReservationCount(int userId);
    
    @Query("SELECT * FROM seat_reservations WHERE userId = :userId AND reservationDate = :date AND status = 'active'")
    List<SeatReservation> getUserReservationsForDate(int userId, String date);
}
