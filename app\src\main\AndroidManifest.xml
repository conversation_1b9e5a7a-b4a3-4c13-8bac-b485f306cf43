<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 相机权限（用于头像拍摄） -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 存储权限（用于图片选择） -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- 硬件特性声明 -->
    <!-- 相机硬件特性（可选，不强制要求） -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:enableOnBackInvokedCallback="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.LL"
        tools:targetApi="31">

        <!-- 启动页 -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.LL.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 登录相关 -->
        <activity
            android:name=".activity.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.LL.NoActionBar" />

        <activity
            android:name=".activity.RegisterActivity"
            android:exported="false" />

        <activity
            android:name=".activity.AdminLoginActivity"
            android:exported="false"
            android:theme="@style/Theme.LL.NoActionBar" />

        <!-- 用户主界面 -->
        <activity
            android:name=".activity.UserMainActivity"
            android:exported="false"
            android:theme="@style/Theme.LL.NoActionBar" />

        <!-- 图书相关 -->
        <activity
            android:name=".activity.BookDetailActivity"
            android:exported="false" />

        <activity
            android:name=".activity.BookSearchActivity"
            android:exported="false" />

        <activity
            android:name=".activity.BookCategoryActivity"
            android:exported="false" />

        <!-- 座位相关 -->
        <activity
            android:name=".activity.SeatReservationActivity"
            android:exported="false" />

        <activity
            android:name=".activity.SeatDetailActivity"
            android:exported="false" />

        <!-- 个人中心 -->
        <activity
            android:name=".activity.ProfileActivity"
            android:exported="false" />

        <activity
            android:name=".activity.SettingsActivity"
            android:exported="false" />

        <!-- 管理员相关 -->
        <activity
            android:name=".activity.AdminMainActivity"
            android:exported="false"
            android:theme="@style/Theme.LL.NoActionBar" />

    </application>

</manifest>