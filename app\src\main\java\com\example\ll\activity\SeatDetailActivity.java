package com.example.ll.activity;

import android.os.Bundle;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.ll.R;
import com.example.ll.adapter.SeatReservationAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Seat;
import com.example.ll.model.SeatReservation;
import com.example.ll.model.StudyRoom;
import com.example.ll.utils.SeatUtils;

import java.util.ArrayList;
import java.util.List;

public class SeatDetailActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private TextView tvSeatNumber, tvSeatType, tvSeatStatus;
    private TextView tvRoomName, tvRoomLocation;
    private TextView tvHasPower, tvHasNetwork;
    private RecyclerView rvReservationHistory;

    private LibraryDatabase database;
    private SeatReservationAdapter reservationAdapter;

    private int seatId;
    private Seat currentSeat;
    private StudyRoom currentRoom;
    private List<SeatReservation> reservationHistory = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_seat_detail);

        // 获取传递的座位ID
        seatId = getIntent().getIntExtra("seat_id", -1);
        if (seatId == -1) {
            Toast.makeText(this, "座位信息错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        initViews();
        initData();
        setListeners();
        loadSeatData();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        tvSeatNumber = findViewById(R.id.tv_seat_number);
        tvSeatType = findViewById(R.id.tv_seat_type);
        tvSeatStatus = findViewById(R.id.tv_seat_status);
        tvRoomName = findViewById(R.id.tv_room_name);
        tvRoomLocation = findViewById(R.id.tv_room_location);
        tvHasPower = findViewById(R.id.tv_has_power);
        tvHasNetwork = findViewById(R.id.tv_has_network);
        rvReservationHistory = findViewById(R.id.rv_reservation_history);

        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("座位详情");
        }

        rvReservationHistory.setLayoutManager(new LinearLayoutManager(this));
    }

    private void initData() {
        database = LibraryDatabase.getInstance(this);

        reservationAdapter = new SeatReservationAdapter(this, reservationHistory);
        rvReservationHistory.setAdapter(reservationAdapter);
    }

    private void setListeners() {
        toolbar.setNavigationOnClickListener(v -> finish());
    }

    private void loadSeatData() {
        new Thread(() -> {
            try {
                // 获取座位信息
                currentSeat = database.seatDao().getSeatById(seatId);
                if (currentSeat == null) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "座位不存在", Toast.LENGTH_SHORT).show();
                        finish();
                    });
                    return;
                }

                // 获取房间信息
                currentRoom = database.studyRoomDao().getRoomById(currentSeat.getRoomId());

                // 获取预约历史
                reservationHistory.clear();
                reservationHistory.addAll(database.seatReservationDao().getReservationsBySeat(seatId));

                runOnUiThread(() -> {
                    displaySeatInfo();
                    reservationAdapter.notifyDataSetChanged();
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载座位信息失败", Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        }).start();
    }

    private void displaySeatInfo() {
        tvSeatNumber.setText("座位 " + currentSeat.getSeatNumber());
        tvSeatType.setText(SeatUtils.getSeatTypeText(currentSeat.getSeatType()));
        tvSeatStatus.setText(SeatUtils.getSeatStatusText(currentSeat.getStatus()));

        if (currentRoom != null) {
            tvRoomName.setText(currentRoom.getRoomName());
            tvRoomLocation.setText(currentRoom.getLocation());
        }

        tvHasPower.setText(currentSeat.isHasPower() ? "有电源" : "无电源");
        tvHasNetwork.setText(currentSeat.isHasNetwork() ? "有网络" : "无网络");
    }
}
