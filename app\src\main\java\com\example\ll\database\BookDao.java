package com.example.ll.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.ll.model.Book;

import java.util.List;

@Dao
public interface BookDao {
    
    @Insert
    long insertBook(Book book);
    
    @Update
    void updateBook(Book book);
    
    @Delete
    void deleteBook(Book book);
    
    @Query("SELECT * FROM books WHERE bookId = :bookId")
    Book getBookById(int bookId);
    
    @Query("SELECT * FROM books WHERE isbn = :isbn")
    Book getBookByIsbn(String isbn);
    
    @Query("SELECT * FROM books WHERE status = 'available' ORDER BY createdAt DESC")
    List<Book> getAllAvailableBooks();
    
    @Query("SELECT * FROM books ORDER BY createdAt DESC")
    List<Book> getAllBooks();
    
    @Query("SELECT * FROM books WHERE categoryId = :categoryId AND status = 'available' ORDER BY title")
    List<Book> getBooksByCategory(int categoryId);
    
    @Query("SELECT * FROM books WHERE (title LIKE :keyword OR author LIKE :keyword OR isbn LIKE :keyword) AND status = 'available'")
    List<Book> searchBooks(String keyword);
    
    @Query("SELECT * FROM books WHERE title LIKE :title AND status = 'available'")
    List<Book> searchBooksByTitle(String title);
    
    @Query("SELECT * FROM books WHERE author LIKE :author AND status = 'available'")
    List<Book> searchBooksByAuthor(String author);
    
    @Query("UPDATE books SET availableCopies = availableCopies - 1 WHERE bookId = :bookId AND availableCopies > 0")
    int borrowBook(int bookId);
    
    @Query("UPDATE books SET availableCopies = availableCopies + 1 WHERE bookId = :bookId")
    void returnBook(int bookId);
    
    @Query("SELECT * FROM books WHERE availableCopies > 0 AND status = 'available' ORDER BY title")
    List<Book> getAvailableBooksForBorrow();
    
    @Query("SELECT COUNT(*) FROM books WHERE status = 'available'")
    int getTotalBookCount();
    
    @Query("SELECT SUM(availableCopies) FROM books WHERE status = 'available'")
    int getAvailableBookCount();
    
    @Query("SELECT * FROM books WHERE status = 'available' ORDER BY createdAt DESC LIMIT :limit")
    List<Book> getLatestBooks(int limit);
    
    @Query("UPDATE books SET status = :status WHERE bookId = :bookId")
    void updateBookStatus(int bookId, String status);
}
