package com.example.ll.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;

import com.example.ll.R;
import com.example.ll.activity.ChangePasswordActivity;
import com.example.ll.activity.LoginActivity;
import com.example.ll.activity.ProfileActivity;
import com.example.ll.activity.ProfileEditActivity;
import com.example.ll.activity.SettingsActivity;
import com.example.ll.activity.UserManagementActivity;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.User;
import com.example.ll.utils.PermissionManager;
import com.example.ll.utils.SharedPreferencesManager;
import com.example.ll.utils.UserUtils;

import de.hdodenhof.circleimageview.CircleImageView;

public class ProfileFragment extends Fragment {

    private CircleImageView ivAvatar;
    private TextView tvNickname, tvUsername, tvEmail, tvPhone;
    private TextView tvBorrowingCount, tvReservationCount, tvMessageCount;
    private LinearLayout llPersonalInfo, llChangePassword, llUserManagement, llSettings, llAbout, llLogout;

    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private PermissionManager permissionManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_profile, container, false);
        
        initViews(view);
        initData();
        setListeners();
        loadUserInfo();
        
        return view;
    }

    private void initViews(View view) {
        ivAvatar = view.findViewById(R.id.iv_avatar);
        tvNickname = view.findViewById(R.id.tv_nickname);
        tvUsername = view.findViewById(R.id.tv_username);
        tvEmail = view.findViewById(R.id.tv_email);
        tvPhone = view.findViewById(R.id.tv_phone);
        
        tvBorrowingCount = view.findViewById(R.id.tv_borrowing_count);
        tvReservationCount = view.findViewById(R.id.tv_reservation_count);
        tvMessageCount = view.findViewById(R.id.tv_message_count);
        
        llPersonalInfo = view.findViewById(R.id.ll_personal_info);
        llChangePassword = view.findViewById(R.id.ll_change_password);
        llUserManagement = view.findViewById(R.id.ll_user_management);
        llSettings = view.findViewById(R.id.ll_settings);
        llAbout = view.findViewById(R.id.ll_about);
        llLogout = view.findViewById(R.id.ll_logout);
    }

    private void initData() {
        database = LibraryDatabase.getInstance(getContext());
        prefsManager = SharedPreferencesManager.getInstance(getContext());
        permissionManager = PermissionManager.getInstance(getContext());
    }

    private void setListeners() {
        llPersonalInfo.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), ProfileEditActivity.class);
            startActivity(intent);
        });
        
        llChangePassword.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), ChangePasswordActivity.class);
            startActivity(intent);
        });

        // 用户管理菜单项（仅管理员可见）
        if (llUserManagement != null) {
            if (permissionManager.canManageUsers()) {
                llUserManagement.setVisibility(View.VISIBLE);
                llUserManagement.setOnClickListener(v -> {
                    Intent intent = new Intent(getContext(), UserManagementActivity.class);
                    startActivity(intent);
                });
            } else {
                llUserManagement.setVisibility(View.GONE);
            }
        }

        llSettings.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), SettingsActivity.class);
            startActivity(intent);
        });
        
        llAbout.setOnClickListener(v -> {
            showAboutDialog();
        });
        
        llLogout.setOnClickListener(v -> {
            showLogoutDialog();
        });
    }

    private void loadUserInfo() {
        new Thread(() -> {
            try {
                int userId = prefsManager.getCurrentUserId();
                User user = database.userDao().getUserById(userId);
                
                // 获取统计数据
                int borrowingCount = database.borrowingRecordDao().getCurrentBorrowingCount(userId);
                int reservationCount = database.seatReservationDao().getActiveReservationCount(userId);
                // 消息数量暂时设为0，后续可以添加消息功能
                int messageCount = 0;
                
                if (getActivity() != null && user != null) {
                    getActivity().runOnUiThread(() -> {
                        // 更新用户信息
                        String displayName = UserUtils.getDisplayName(user);
                        tvNickname.setText(displayName);
                        tvUsername.setText("@" + user.getUsername());
                        
                        if (user.getEmail() != null && !user.getEmail().isEmpty()) {
                            tvEmail.setText(user.getEmail());
                            tvEmail.setVisibility(View.VISIBLE);
                        } else {
                            tvEmail.setVisibility(View.GONE);
                        }
                        
                        if (user.getPhone() != null && !user.getPhone().isEmpty()) {
                            tvPhone.setText(user.getPhone());
                            tvPhone.setVisibility(View.VISIBLE);
                        } else {
                            tvPhone.setVisibility(View.GONE);
                        }
                        
                        // 更新统计数据
                        tvBorrowingCount.setText(String.valueOf(borrowingCount));
                        tvReservationCount.setText(String.valueOf(reservationCount));
                        tvMessageCount.setText(String.valueOf(messageCount));
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    private void showAboutDialog() {
        new AlertDialog.Builder(getContext())
                .setTitle("关于掌上图书馆")
                .setMessage("掌上图书馆 v1.0\n\n一款便捷的图书馆管理应用，提供图书借阅、座位预约等功能。\n\n© 2024 掌上图书馆")
                .setPositiveButton("确定", null)
                .show();
    }

    private void showLogoutDialog() {
        new AlertDialog.Builder(getContext())
                .setTitle("退出登录")
                .setMessage("确定要退出登录吗？")
                .setPositiveButton("确定", (dialog, which) -> {
                    logout();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void logout() {
        // 清除登录信息
        prefsManager.clearUserLogin();
        
        // 跳转到登录页面
        Intent intent = new Intent(getContext(), LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        
        if (getActivity() != null) {
            getActivity().finish();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        loadUserInfo(); // 页面恢复时刷新用户信息
    }
}
