<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_category"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground">

    <TextView
        android:id="@+id/tv_category_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="分类名称"
        android:textSize="14sp"
        android:textColor="@color/text_primary"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:minWidth="60dp"
        android:gravity="center" />

</androidx.cardview.widget.CardView>
