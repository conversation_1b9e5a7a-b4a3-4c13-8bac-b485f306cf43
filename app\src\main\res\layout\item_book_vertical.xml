<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_book"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:id="@+id/iv_book_cover"
            android:layout_width="80dp"
            android:layout_height="100dp"
            android:scaleType="centerCrop"
            android:background="@color/background_color"
            android:contentDescription="图书封面" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="16dp">

            <TextView
                android:id="@+id/tv_book_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="图书标题"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="2"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tv_book_author"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="作者"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginTop="8dp" />

            <TextView
                android:id="@+id/tv_book_available"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="可借：5本"
                android:textSize="14sp"
                android:textColor="@color/success_color"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
