package com.example.ll.database;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.example.ll.model.Book;
import com.example.ll.model.BookCategory;
import com.example.ll.model.BorrowingRecord;
import com.example.ll.model.Seat;
import com.example.ll.model.SeatReservation;
import com.example.ll.model.StudyRoom;
import com.example.ll.model.User;

@Database(
    entities = {
        User.class,
        Book.class,
        BookCategory.class,
        BorrowingRecord.class,
        StudyRoom.class,
        Seat.class,
        SeatReservation.class
    },
    version = 1,
    exportSchema = false
)
public abstract class LibraryDatabase extends RoomDatabase {
    
    private static final String DATABASE_NAME = "library_database";
    private static volatile LibraryDatabase INSTANCE;
    
    // DAO方法
    public abstract UserDao userDao();
    public abstract BookDao bookDao();
    public abstract BookCategoryDao bookCategoryDao();
    public abstract BorrowingRecordDao borrowingRecordDao();
    public abstract StudyRoomDao studyRoomDao();
    public abstract SeatDao seatDao();
    public abstract SeatReservationDao seatReservationDao();
    
    // 获取数据库实例（单例模式）
    public static LibraryDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (LibraryDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            LibraryDatabase.class,
                            DATABASE_NAME
                    )
                    .allowMainThreadQueries() // 允许在主线程查询（仅用于演示，生产环境建议使用异步）
                    .build();
                    
                    // 初始化默认数据
                    initializeDefaultData(INSTANCE);
                }
            }
        }
        return INSTANCE;
    }
    
    // 初始化默认数据
    private static void initializeDefaultData(LibraryDatabase database) {
        // 在后台线程中初始化数据
        new Thread(() -> {
            // 检查是否已有数据
            if (database.bookCategoryDao().getCategoryCount() == 0) {
                initializeCategories(database);
            }
            
            if (database.bookDao().getTotalBookCount() == 0) {
                initializeSampleBooks(database);
            }
            
            if (database.studyRoomDao().getRoomCount() == 0) {
                initializeStudyRooms(database);
            }
        }).start();
    }
    
    // 初始化图书分类
    private static void initializeCategories(LibraryDatabase database) {
        BookCategory[] categories = {
            new BookCategory("文学", "文学类图书"),
            new BookCategory("科技", "科技类图书"),
            new BookCategory("历史", "历史类图书"),
            new BookCategory("艺术", "艺术类图书"),
            new BookCategory("教育", "教育类图书"),
            new BookCategory("经济", "经济管理类图书"),
            new BookCategory("医学", "医学类图书"),
            new BookCategory("法律", "法律类图书")
        };
        
        for (BookCategory category : categories) {
            database.bookCategoryDao().insertCategory(category);
        }
    }
    
    // 初始化示例图书
    private static void initializeSampleBooks(LibraryDatabase database) {
        Book[] books = {
            createBook("红楼梦", "曹雪芹", "9787020002207", 1, "中国古典文学四大名著之一", 5),
            createBook("西游记", "吴承恩", "9787020002214", 1, "中国古典文学四大名著之一", 3),
            createBook("水浒传", "施耐庵", "9787020002221", 1, "中国古典文学四大名著之一", 4),
            createBook("三国演义", "罗贯中", "9787020002238", 1, "中国古典文学四大名著之一", 6),
            createBook("Java编程思想", "Bruce Eckel", "9787111213826", 2, "Java编程经典教材", 8),
            createBook("算法导论", "Thomas H. Cormen", "9787111407010", 2, "计算机算法经典教材", 5),
            createBook("史记", "司马迁", "9787101003048", 3, "中国第一部纪传体通史", 3),
            createBook("资治通鉴", "司马光", "9787101003055", 3, "中国编年体史书", 2)
        };
        
        for (Book book : books) {
            database.bookDao().insertBook(book);
        }
    }
    
    // 创建图书对象的辅助方法
    private static Book createBook(String title, String author, String isbn, int categoryId, String description, int copies) {
        Book book = new Book(title, author, isbn);
        book.setCategoryId(categoryId);
        book.setDescription(description);
        book.setTotalCopies(copies);
        book.setAvailableCopies(copies);
        book.setLocation("A区书架");
        book.setPrice(29.80);
        return book;
    }
    
    // 初始化自习室
    private static void initializeStudyRooms(LibraryDatabase database) {
        StudyRoom[] rooms = {
            new StudyRoom("自习室A", 50, "图书馆一楼"),
            new StudyRoom("自习室B", 40, "图书馆二楼"),
            new StudyRoom("阅览室C", 60, "图书馆三楼"),
            new StudyRoom("电子阅览室", 30, "图书馆四楼")
        };
        
        rooms[3].setRoomType("computer_room");
        
        for (StudyRoom room : rooms) {
            long roomId = database.studyRoomDao().insertRoom(room);
            initializeSeatsForRoom(database, (int) roomId, room.getCapacity());
        }
    }
    
    // 为房间初始化座位
    private static void initializeSeatsForRoom(LibraryDatabase database, int roomId, int capacity) {
        for (int i = 1; i <= capacity; i++) {
            Seat seat = new Seat(roomId, String.format("%03d", i));
            if (i % 5 == 0) { // 每5个座位有一个带电源
                seat.setHasPower(true);
            }
            if (i % 10 == 0) { // 每10个座位有一个带网络
                seat.setHasNetwork(true);
            }
            database.seatDao().insertSeat(seat);
        }
    }
    
    // 关闭数据库
    public static void destroyInstance() {
        if (INSTANCE != null) {
            INSTANCE.close();
            INSTANCE = null;
        }
    }
}
