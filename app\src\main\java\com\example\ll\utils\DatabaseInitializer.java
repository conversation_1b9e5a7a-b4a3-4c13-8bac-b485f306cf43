package com.example.ll.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Book;
import com.example.ll.model.BookCategory;
import com.example.ll.model.Seat;
import com.example.ll.model.StudyRoom;
import com.example.ll.model.User;

import java.util.ArrayList;
import java.util.List;

public class DatabaseInitializer {

    private static final String PREF_NAME = "DatabaseInitializer";
    private static final String PREF_KEY_INITIALIZED = "database_initialized";

    public static void initializeIfNeeded(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);

        // 检查是否已经初始化过
        if (prefs.getBoolean(PREF_KEY_INITIALIZED, false)) {
            return;
        }

        // 在后台线程中初始化数据库
        new Thread(() -> {
            try {
                LibraryDatabase database = LibraryDatabase.getInstance(context);

                // 初始化数据
                initializeBookCategories(database);
                initializeBooks(database);
                initializeUsers(database);
                initializeSeatsAndRooms(database);

                // 标记为已初始化
                prefs.edit()
                        .putBoolean(PREF_KEY_INITIALIZED, true)
                        .apply();

            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }
    
    private static void initializeBookCategories(LibraryDatabase database) {
        List<BookCategory> categories = new ArrayList<>();
        
        BookCategory category1 = new BookCategory("计算机科学", "编程、算法、软件工程等");
        category1.setCategoryId(1);
        categories.add(category1);
        
        BookCategory category2 = new BookCategory("文学", "小说、诗歌、散文等");
        category2.setCategoryId(2);
        categories.add(category2);
        
        BookCategory category3 = new BookCategory("历史", "中外历史、传记等");
        category3.setCategoryId(3);
        categories.add(category3);
        
        BookCategory category4 = new BookCategory("科学", "物理、化学、生物等");
        category4.setCategoryId(4);
        categories.add(category4);
        
        for (BookCategory category : categories) {
            database.bookCategoryDao().insertCategory(category);
        }
    }
    
    private static void initializeBooks(LibraryDatabase database) {
        List<Book> books = new ArrayList<>();
        
        // 计算机类图书
        Book book1 = new Book("Java编程思想", "Bruce Eckel", "9787111213826");
        book1.setCategoryId(1);
        book1.setPublisher("机械工业出版社");
        book1.setPublishDate("2007-06-01");
        book1.setDescription("《Java编程思想》是Java程序设计领域的经典之作，深入浅出地介绍了Java语言的核心概念和编程技巧。");
        book1.setLocation("A区-2楼-计算机类-001");
        book1.setTotalCopies(5);
        book1.setAvailableCopies(3);
        books.add(book1);
        
        Book book2 = new Book("Effective Java", "Joshua Bloch", "9787111255833");
        book2.setCategoryId(1);
        book2.setPublisher("机械工业出版社");
        book2.setPublishDate("2009-01-01");
        book2.setDescription("《Effective Java》是Java之父James Gosling强烈推荐的Java进阶读物，包含了78条极具实用价值的经验规则。");
        book2.setLocation("A区-2楼-计算机类-002");
        book2.setTotalCopies(3);
        book2.setAvailableCopies(2);
        books.add(book2);
        
        Book book3 = new Book("Android开发艺术探索", "任玉刚", "9787121269394");
        book3.setCategoryId(1);
        book3.setPublisher("电子工业出版社");
        book3.setPublishDate("2015-09-01");
        book3.setDescription("本书是Android开发进阶类书籍，采用理论、源码和实践相结合的方式来阐述高水准的Android应用开发要点。");
        book3.setLocation("A区-2楼-计算机类-003");
        book3.setTotalCopies(2);
        book3.setAvailableCopies(1);
        books.add(book3);
        
        Book book4 = new Book("Spring Boot实战", "Craig Walls", "9787115416179");
        book4.setCategoryId(1);
        book4.setPublisher("人民邮电出版社");
        book4.setPublishDate("2016-09-01");
        book4.setDescription("Spring Boot是Spring框架的一个子项目，旨在简化Spring应用的创建、运行、调试、部署等。");
        book4.setLocation("A区-2楼-计算机类-004");
        book4.setTotalCopies(4);
        book4.setAvailableCopies(3);
        books.add(book4);
        
        Book book5 = new Book("深入理解Java虚拟机", "周志明", "9787111421900");
        book5.setCategoryId(1);
        book5.setPublisher("机械工业出版社");
        book5.setPublishDate("2013-09-01");
        book5.setDescription("本书深入浅出地介绍了Java虚拟机的工作原理，是Java程序员进阶必读的经典著作。");
        book5.setLocation("A区-2楼-计算机类-005");
        book5.setTotalCopies(3);
        book5.setAvailableCopies(2);
        books.add(book5);
        
        Book book6 = new Book("设计模式", "Gang of Four", "9787111075776");
        book6.setCategoryId(1);
        book6.setPublisher("机械工业出版社");
        book6.setPublishDate("2000-09-01");
        book6.setDescription("本书是设计模式领域的经典之作，介绍了23种常用的设计模式，是软件工程师必备的参考书。");
        book6.setLocation("A区-2楼-计算机类-006");
        book6.setTotalCopies(5);
        book6.setAvailableCopies(4);
        books.add(book6);
        
        Book book7 = new Book("算法导论", "Thomas H. Cormen", "9787111407010");
        book7.setCategoryId(1);
        book7.setPublisher("机械工业出版社");
        book7.setPublishDate("2012-12-01");
        book7.setDescription("本书深入浅出地介绍了众多算法及相关的数据结构，以及用于解决这些算法的技术。");
        book7.setLocation("A区-2楼-计算机类-007");
        book7.setTotalCopies(4);
        book7.setAvailableCopies(0);
        books.add(book7);
        
        // 文学类图书
        Book book8 = new Book("红楼梦", "曹雪芹", "9787020002207");
        book8.setCategoryId(2);
        book8.setPublisher("人民文学出版社");
        book8.setPublishDate("1996-12-01");
        book8.setDescription("《红楼梦》是中国古典四大名著之首，是一部具有世界影响力的人情小说作品。");
        book8.setLocation("B区-1楼-文学类-001");
        book8.setTotalCopies(8);
        book8.setAvailableCopies(5);
        books.add(book8);
        
        Book book9 = new Book("西游记", "吴承恩", "9787020002214");
        book9.setCategoryId(2);
        book9.setPublisher("人民文学出版社");
        book9.setPublishDate("1996-12-01");
        book9.setDescription("《西游记》是中国古典四大名著之一，是一部优秀的神话小说。");
        book9.setLocation("B区-1楼-文学类-002");
        book9.setTotalCopies(6);
        book9.setAvailableCopies(4);
        books.add(book9);
        
        Book book10 = new Book("三体", "刘慈欣", "9787536692930");
        book10.setCategoryId(2);
        book10.setPublisher("重庆出版社");
        book10.setPublishDate("2006-05-01");
        book10.setDescription("《三体》是刘慈欣创作的系列长篇科幻小说，是中国科幻文学的里程碑之作。");
        book10.setLocation("B区-1楼-文学类-003");
        book10.setTotalCopies(8);
        book10.setAvailableCopies(6);
        books.add(book10);
        
        // 历史类图书
        Book book11 = new Book("人类简史", "尤瓦尔·赫拉利", "9787508647357");
        book11.setCategoryId(3);
        book11.setPublisher("中信出版社");
        book11.setPublishDate("2014-11-01");
        book11.setDescription("《人类简史》是以色列新锐历史学家尤瓦尔·赫拉利的代表作品。");
        book11.setLocation("C区-1楼-历史类-001");
        book11.setTotalCopies(4);
        book11.setAvailableCopies(3);
        books.add(book11);
        
        for (Book book : books) {
            database.bookDao().insertBook(book);
        }
    }
    
    private static void initializeUsers(LibraryDatabase database) {
        // 创建默认用户账号
        User defaultUser = new User("admin", "123456", "<EMAIL>", "13800138000");
        defaultUser.setNickname("管理员");
        defaultUser.setStatus("active");
        database.userDao().insertUser(defaultUser);
        
        User testUser = new User("user", "123456", "<EMAIL>", "13800138001");
        testUser.setNickname("测试用户");
        testUser.setStatus("active");
        database.userDao().insertUser(testUser);
        
        User student1 = new User("student1", "123456", "<EMAIL>", "13800138002");
        student1.setNickname("张三");
        student1.setStatus("active");
        database.userDao().insertUser(student1);
        
        User student2 = new User("student2", "123456", "<EMAIL>", "13800138003");
        student2.setNickname("李四");
        student2.setStatus("active");
        database.userDao().insertUser(student2);
    }
    
    private static void initializeSeatsAndRooms(LibraryDatabase database) {
        // 创建学习室
        StudyRoom room1 = new StudyRoom("自习室A", 50, "图书馆1楼");
        room1.setRoomType("study_room");
        room1.setFacilities("空调、WiFi、插座");
        long roomId1 = database.studyRoomDao().insertRoom(room1);
        
        StudyRoom room2 = new StudyRoom("阅览室B", 30, "图书馆2楼");
        room2.setRoomType("reading_room");
        room2.setFacilities("安静环境、自然光");
        long roomId2 = database.studyRoomDao().insertRoom(room2);
        
        StudyRoom room3 = new StudyRoom("讨论室C", 20, "图书馆3楼");
        room3.setRoomType("discussion_room");
        room3.setFacilities("白板、投影仪、音响");
        long roomId3 = database.studyRoomDao().insertRoom(room3);
        
        // 为每个房间创建座位
        for (int i = 1; i <= 25; i++) {
            Seat seat = new Seat((int)roomId1, "A" + String.format("%02d", i));
            seat.setSeatType("single");
            seat.setHasPower(true);
            seat.setHasNetwork(true);
            database.seatDao().insertSeat(seat);
        }
        
        for (int i = 1; i <= 15; i++) {
            Seat seat = new Seat((int)roomId2, "B" + String.format("%02d", i));
            seat.setSeatType("single");
            seat.setHasPower(false);
            seat.setHasNetwork(true);
            database.seatDao().insertSeat(seat);
        }
        
        for (int i = 1; i <= 10; i++) {
            Seat seat = new Seat((int)roomId3, "C" + String.format("%02d", i));
            seat.setSeatType("group");
            seat.setHasPower(true);
            seat.setHasNetwork(true);
            database.seatDao().insertSeat(seat);
        }
    }
}
