package com.example.ll.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.ll.model.BorrowingRecord;

import java.util.List;

@Dao
public interface BorrowingRecordDao {
    
    @Insert
    long insertBorrowingRecord(BorrowingRecord record);
    
    @Update
    void updateBorrowingRecord(BorrowingRecord record);
    
    @Delete
    void deleteBorrowingRecord(BorrowingRecord record);
    
    @Query("SELECT * FROM borrowing_records WHERE recordId = :recordId")
    BorrowingRecord getBorrowingRecordById(int recordId);
    
    @Query("SELECT * FROM borrowing_records WHERE userId = :userId ORDER BY borrowDate DESC")
    List<BorrowingRecord> getBorrowingRecordsByUser(int userId);
    
    @Query("SELECT * FROM borrowing_records WHERE userId = :userId AND status = 'borrowed' ORDER BY dueDate ASC")
    List<BorrowingRecord> getCurrentBorrowingsByUser(int userId);
    
    @Query("SELECT * FROM borrowing_records WHERE bookId = :bookId ORDER BY borrowDate DESC")
    List<BorrowingRecord> getBorrowingRecordsByBook(int bookId);
    
    @Query("SELECT * FROM borrowing_records WHERE status = 'borrowed' ORDER BY dueDate ASC")
    List<BorrowingRecord> getAllCurrentBorrowings();
    
    @Query("SELECT * FROM borrowing_records WHERE status = 'borrowed' AND dueDate < :currentTime ORDER BY dueDate ASC")
    List<BorrowingRecord> getOverdueRecords(long currentTime);
    
    @Query("SELECT * FROM borrowing_records WHERE userId = :userId AND status = 'borrowed' AND dueDate < :currentTime")
    List<BorrowingRecord> getUserOverdueRecords(int userId, long currentTime);
    
    @Query("SELECT * FROM borrowing_records WHERE status = 'returned' ORDER BY returnDate DESC")
    List<BorrowingRecord> getReturnedRecords();
    
    @Query("UPDATE borrowing_records SET status = 'returned', returnDate = :returnDate WHERE recordId = :recordId")
    void returnBook(int recordId, long returnDate);
    
    @Query("UPDATE borrowing_records SET dueDate = :newDueDate, renewalCount = renewalCount + 1 WHERE recordId = :recordId")
    void renewBook(int recordId, long newDueDate);
    
    @Query("SELECT COUNT(*) FROM borrowing_records WHERE userId = :userId AND status = 'borrowed'")
    int getCurrentBorrowingCount(int userId);
    
    @Query("SELECT * FROM borrowing_records WHERE userId = :userId AND bookId = :bookId AND status = 'borrowed'")
    BorrowingRecord getCurrentBorrowingRecord(int userId, int bookId);
    
    @Query("SELECT * FROM borrowing_records WHERE status = 'borrowed' AND dueDate BETWEEN :startTime AND :endTime")
    List<BorrowingRecord> getBorrowingsDueSoon(long startTime, long endTime);
    
    @Query("UPDATE borrowing_records SET status = 'overdue' WHERE status = 'borrowed' AND dueDate < :currentTime")
    void updateOverdueRecords(long currentTime);
    
    @Query("SELECT COUNT(*) FROM borrowing_records WHERE status = 'borrowed'")
    int getTotalBorrowingCount();
    
    @Query("SELECT COUNT(*) FROM borrowing_records WHERE status = 'overdue'")
    int getOverdueCount();
}
