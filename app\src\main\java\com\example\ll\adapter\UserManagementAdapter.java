package com.example.ll.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.ll.R;
import com.example.ll.model.User;
import com.example.ll.utils.DateUtils;
import com.example.ll.utils.PermissionManager;
import com.example.ll.utils.UserUtils;

import java.util.List;

public class UserManagementAdapter extends RecyclerView.Adapter<UserManagementAdapter.UserViewHolder> {

    private Context context;
    private List<User> userList;
    private OnUserActionListener listener;
    private PermissionManager permissionManager;

    public interface OnUserActionListener {
        void onUserClick(User user);
        void onUserEdit(User user);
        void onUserDelete(User user);
        void onUserFreeze(User user);
    }

    public UserManagementAdapter(Context context, List<User> userList, OnUserActionListener listener) {
        this.context = context;
        this.userList = userList;
        this.listener = listener;
        this.permissionManager = PermissionManager.getInstance(context);
    }

    @NonNull
    @Override
    public UserViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_user_management, parent, false);
        return new UserViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull UserViewHolder holder, int position) {
        User user = userList.get(position);
        holder.bind(user);
    }

    @Override
    public int getItemCount() {
        return userList.size();
    }

    class UserViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivAvatar, ivEdit, ivDelete, ivFreeze;
        private TextView tvDisplayName, tvUsername, tvRole, tvStatus, tvEmail, tvPhone;
        private TextView tvCreatedTime, tvLastLogin;
        private View statusIndicator;

        public UserViewHolder(@NonNull View itemView) {
            super(itemView);
            
            ivAvatar = itemView.findViewById(R.id.iv_avatar);
            ivEdit = itemView.findViewById(R.id.iv_edit);
            ivDelete = itemView.findViewById(R.id.iv_delete);
            ivFreeze = itemView.findViewById(R.id.iv_freeze);
            
            tvDisplayName = itemView.findViewById(R.id.tv_display_name);
            tvUsername = itemView.findViewById(R.id.tv_username);
            tvRole = itemView.findViewById(R.id.tv_role);
            tvStatus = itemView.findViewById(R.id.tv_status);
            tvEmail = itemView.findViewById(R.id.tv_email);
            tvPhone = itemView.findViewById(R.id.tv_phone);
            tvCreatedTime = itemView.findViewById(R.id.tv_created_time);
            tvLastLogin = itemView.findViewById(R.id.tv_last_login);
            
            statusIndicator = itemView.findViewById(R.id.status_indicator);
        }

        public void bind(User user) {
            // 设置用户基本信息
            tvDisplayName.setText(UserUtils.getDisplayName(user));
            tvUsername.setText("@" + user.getUsername());
            tvRole.setText(UserUtils.getRoleDisplayText(user.getRole()));
            tvStatus.setText(UserUtils.getStatusDisplayText(user.getStatus()));
            
            // 设置联系信息
            if (user.getEmail() != null && !user.getEmail().isEmpty()) {
                tvEmail.setText(user.getEmail());
                tvEmail.setVisibility(View.VISIBLE);
            } else {
                tvEmail.setVisibility(View.GONE);
            }
            
            if (user.getPhone() != null && !user.getPhone().isEmpty()) {
                tvPhone.setText(user.getPhone());
                tvPhone.setVisibility(View.VISIBLE);
            } else {
                tvPhone.setVisibility(View.GONE);
            }
            
            // 设置时间信息
            tvCreatedTime.setText("注册：" + DateUtils.formatDisplayDate(user.getCreatedAt()));
            tvLastLogin.setText("最后登录：" + UserUtils.formatLastLoginTime(user.getLastLoginTime()));
            
            // 设置状态指示器
            setStatusIndicator(user);
            
            // 设置角色颜色
            setRoleColor(user);
            
            // 设置操作按钮可见性和点击事件
            setupActionButtons(user);
            
            // 设置整体点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onUserClick(user);
                }
            });
        }
        
        private void setStatusIndicator(User user) {
            int color;
            switch (user.getStatus()) {
                case UserUtils.STATUS_ACTIVE:
                    color = context.getResources().getColor(R.color.success_color, null);
                    break;
                case UserUtils.STATUS_FROZEN:
                    color = context.getResources().getColor(R.color.warning_dark, null);
                    break;
                case UserUtils.STATUS_DELETED:
                    color = context.getResources().getColor(R.color.error_color, null);
                    break;
                default:
                    color = context.getResources().getColor(R.color.text_hint, null);
                    break;
            }
            statusIndicator.setBackgroundColor(color);
        }
        
        private void setRoleColor(User user) {
            int color;
            switch (user.getRole()) {
                case UserUtils.ROLE_ADMIN:
                    color = context.getResources().getColor(R.color.error_color, null);
                    break;
                case UserUtils.ROLE_LIBRARIAN:
                    color = context.getResources().getColor(R.color.primary_color, null);
                    break;
                case UserUtils.ROLE_USER:
                    color = context.getResources().getColor(R.color.text_secondary, null);
                    break;
                default:
                    color = context.getResources().getColor(R.color.text_hint, null);
                    break;
            }
            tvRole.setTextColor(color);
        }
        
        private void setupActionButtons(User user) {
            // 编辑按钮
            if (permissionManager.canEditUser(user)) {
                ivEdit.setVisibility(View.VISIBLE);
                ivEdit.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onUserEdit(user);
                    }
                });
            } else {
                ivEdit.setVisibility(View.GONE);
            }
            
            // 删除按钮
            if (permissionManager.canDeleteUser(user)) {
                ivDelete.setVisibility(View.VISIBLE);
                ivDelete.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onUserDelete(user);
                    }
                });
            } else {
                ivDelete.setVisibility(View.GONE);
            }
            
            // 冻结/解冻按钮
            if (permissionManager.canFreezeUser(user)) {
                ivFreeze.setVisibility(View.VISIBLE);
                
                // 根据当前状态设置图标
                if (UserUtils.isUserFrozen(user)) {
                    ivFreeze.setImageResource(R.drawable.ic_unfreeze);
                    ivFreeze.setContentDescription("解冻用户");
                } else {
                    ivFreeze.setImageResource(R.drawable.ic_freeze);
                    ivFreeze.setContentDescription("冻结用户");
                }
                
                ivFreeze.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onUserFreeze(user);
                    }
                });
            } else {
                ivFreeze.setVisibility(View.GONE);
            }
            
            // 如果没有任何操作权限，隐藏所有按钮
            if (ivEdit.getVisibility() == View.GONE && 
                ivDelete.getVisibility() == View.GONE && 
                ivFreeze.getVisibility() == View.GONE) {
                // 可以添加一个"查看详情"的图标
            }
        }
    }
}
