<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="12dp"
    android:layout_marginEnd="8dp"
    android:background="@drawable/bg_date_selector"
    android:clickable="true"
    android:focusable="true">

    <TextView
        android:id="@+id/tv_day_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="今天"
        android:textSize="12sp"
        android:textColor="@color/text_secondary" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="12/25"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginTop="4dp" />

    <TextView
        android:id="@+id/tv_weekday"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="周一"
        android:textSize="10sp"
        android:textColor="@color/text_hint"
        android:layout_marginTop="2dp" />

</LinearLayout>
