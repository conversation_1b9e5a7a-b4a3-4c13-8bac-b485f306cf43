<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 顶部搜索栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_color"
        android:elevation="4dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:cardCornerRadius="24dp"
            app:cardElevation="0dp"
            android:layout_marginEnd="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/background_color"
                android:padding="12dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_search"
                    app:tint="@color/text_hint"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="8dp" />

                <EditText
                    android:id="@+id/et_search"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="搜索图书、作者、ISBN"
                    android:textSize="14sp"
                    android:textColorHint="@color/text_hint"
                    android:background="@null"
                    android:maxLines="1"
                    android:imeOptions="actionSearch" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <ImageView
            android:id="@+id/iv_category"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_category"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            app:tint="@color/primary_color"
            android:contentDescription="分类" />

    </LinearLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- 分类横向列表 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@color/surface_color"
                android:paddingBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="图书分类"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_categories"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="16dp" />

            </LinearLayout>

            <!-- 图书列表 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_marginTop="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="图书列表"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_books"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </LinearLayout>

        </LinearLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>
