<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center">

        <!-- Logo区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="60dp"
            android:layout_marginBottom="48dp">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_admin_shield"
                android:layout_marginBottom="16dp"
                android:contentDescription="管理员Logo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="掌上图书馆"
                android:textSize="28sp"
                android:textColor="@color/error_color"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="管理员登录"
                android:textSize="16sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <!-- 登录表单 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:layout_marginBottom="24dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- 管理员用户名输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:startIconDrawable="@drawable/ic_admin"
                    app:hintTextColor="@color/error_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_username"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="管理员用户名"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:startIconDrawable="@drawable/ic_lock"
                    app:endIconMode="password_toggle"
                    app:hintTextColor="@color/error_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="管理员密码"
                        android:inputType="textPassword"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 记住密码 -->
                <CheckBox
                    android:id="@+id/cb_remember_password"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="记住密码"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="24dp" />

                <!-- 登录按钮 -->
                <Button
                    android:id="@+id/btn_login"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="管理员登录"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:background="@drawable/button_admin"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="16dp" />

                <!-- 返回用户登录 -->
                <TextView
                    android:id="@+id/tv_back_to_user"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="返回用户登录"
                    android:textColor="@color/primary_color"
                    android:textSize="14sp"
                    android:padding="8dp"
                    android:background="?android:attr/selectableItemBackground"
                    android:layout_gravity="center" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 演示账号提示 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/warning_color"
            android:layout_marginBottom="24dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="演示账号"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="用户名：superadmin"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="2dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="密码：admin123"
                    android:textSize="12sp"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 版权信息 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="© 2024 掌上图书馆管理系统"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:layout_marginTop="24dp" />

    </LinearLayout>

</ScrollView>
