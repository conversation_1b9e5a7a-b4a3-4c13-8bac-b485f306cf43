package com.example.ll.activity;

import android.app.TimePickerDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.ll.R;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Seat;
import com.example.ll.model.SeatReservation;
import com.example.ll.model.StudyRoom;
import com.example.ll.utils.DateUtils;
import com.example.ll.utils.SeatUtils;
import com.example.ll.utils.SharedPreferencesManager;
import com.example.ll.view.SeatGridView;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class SeatReservationActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private TextView tvRoomName, tvRoomInfo, tvRoomFacilities;
    private LinearLayout llDateSelector;
    private TextView tvStartTime, tvEndTime;
    private TextView tvSelectedSeat;
    private SeatGridView seatGridView;
    private Button btnReserve;

    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;

    private int roomId;
    private StudyRoom currentRoom;
    private List<Seat> roomSeats = new ArrayList<>();
    private List<SeatReservation> existingReservations = new ArrayList<>();

    private String selectedDate;
    private String selectedStartTime = "09:00";
    private String selectedEndTime = "12:00";
    private Seat selectedSeat;

    private List<View> dateViews = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_seat_reservation);

        // 获取传递的房间ID
        roomId = getIntent().getIntExtra("room_id", -1);
        if (roomId == -1) {
            // 如果没有传递房间ID，显示房间选择界面
            database = LibraryDatabase.getInstance(this);
            showRoomSelection();
            return;
        }

        initViews();
        initData();
        setListeners();
        loadRoomData();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        tvRoomName = findViewById(R.id.tv_room_name);
        tvRoomInfo = findViewById(R.id.tv_room_info);
        tvRoomFacilities = findViewById(R.id.tv_room_facilities);
        llDateSelector = findViewById(R.id.ll_date_selector);
        tvStartTime = findViewById(R.id.tv_start_time);
        tvEndTime = findViewById(R.id.tv_end_time);
        tvSelectedSeat = findViewById(R.id.tv_selected_seat);
        seatGridView = findViewById(R.id.seat_grid_view);
        btnReserve = findViewById(R.id.btn_reserve);

        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void initData() {
        database = LibraryDatabase.getInstance(this);
        prefsManager = SharedPreferencesManager.getInstance(this);

        selectedDate = DateUtils.getCurrentDate();

        // 初始化日期选择器
        setupDateSelector();
    }

    private void setListeners() {
        toolbar.setNavigationOnClickListener(v -> finish());

        tvStartTime.setOnClickListener(v -> showTimePickerDialog(true));
        tvEndTime.setOnClickListener(v -> showTimePickerDialog(false));

        seatGridView.setOnSeatClickListener(seat -> {
            selectedSeat = seat;
            tvSelectedSeat.setText("座位 " + seat.getSeatNumber());
            updateReserveButton();
        });

        btnReserve.setOnClickListener(v -> performReservation());
    }

    private void setupDateSelector() {
        llDateSelector.removeAllViews();
        dateViews.clear();

        // 显示今天和未来6天
        for (int i = 0; i < 7; i++) {
            String date = DateUtils.getDateAfterDays(i);
            View dateView = createDateView(date, i == 0);
            llDateSelector.addView(dateView);
            dateViews.add(dateView);
        }
    }

    private View createDateView(String date, boolean isSelected) {
        View view = LayoutInflater.from(this).inflate(R.layout.item_date_selector, llDateSelector, false);

        TextView tvDayName = view.findViewById(R.id.tv_day_name);
        TextView tvDate = view.findViewById(R.id.tv_date);
        TextView tvWeekday = view.findViewById(R.id.tv_weekday);

        // 设置日期显示
        if (DateUtils.isToday(date)) {
            tvDayName.setText("今天");
        } else if (DateUtils.isTomorrow(date)) {
            tvDayName.setText("明天");
        } else {
            tvDayName.setText(DateUtils.getFriendlyDate(date));
        }

        // 设置日期和星期
        try {
            Calendar calendar = Calendar.getInstance();
            String[] dateParts = date.split("-");
            calendar.set(Integer.parseInt(dateParts[0]),
                        Integer.parseInt(dateParts[1]) - 1,
                        Integer.parseInt(dateParts[2]));

            tvDate.setText(String.format("%d/%d",
                calendar.get(Calendar.MONTH) + 1,
                calendar.get(Calendar.DAY_OF_MONTH)));

            String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
            tvWeekday.setText(weekDays[calendar.get(Calendar.DAY_OF_WEEK) - 1]);
        } catch (Exception e) {
            tvDate.setText(date);
            tvWeekday.setText("");
        }

        view.setSelected(isSelected);
        if (isSelected) {
            selectedDate = date;
        }

        view.setOnClickListener(v -> {
            // 更新选中状态
            for (View dateView : dateViews) {
                dateView.setSelected(false);
            }
            view.setSelected(true);
            selectedDate = date;

            // 重新加载座位数据
            loadSeatsData();
        });

        return view;
    }

    private void showTimePickerDialog(boolean isStartTime) {
        String currentTime = isStartTime ? selectedStartTime : selectedEndTime;
        String[] timeParts = currentTime.split(":");
        int hour = Integer.parseInt(timeParts[0]);
        int minute = Integer.parseInt(timeParts[1]);

        TimePickerDialog dialog = new TimePickerDialog(this, (view, hourOfDay, minuteOfHour) -> {
            String newTime = String.format("%02d:%02d", hourOfDay, minuteOfHour);

            if (isStartTime) {
                selectedStartTime = newTime;
                tvStartTime.setText(newTime);

                // 确保结束时间晚于开始时间
                if (DateUtils.compareTime(selectedStartTime, selectedEndTime) >= 0) {
                    selectedEndTime = SeatUtils.addMinutes(selectedStartTime, 60);
                    tvEndTime.setText(selectedEndTime);
                }
            } else {
                // 确保结束时间晚于开始时间
                if (DateUtils.compareTime(selectedStartTime, newTime) >= 0) {
                    Toast.makeText(this, "结束时间必须晚于开始时间", Toast.LENGTH_SHORT).show();
                    return;
                }
                selectedEndTime = newTime;
                tvEndTime.setText(newTime);
            }

            // 检查时长是否合理
            if (!SeatUtils.isValidReservationDuration(selectedStartTime, selectedEndTime)) {
                Toast.makeText(this, "预约时长应在30分钟到8小时之间", Toast.LENGTH_SHORT).show();
                return;
            }

            // 重新加载座位数据
            loadSeatsData();
            updateReserveButton();

        }, hour, minute, true);

        dialog.show();
    }

    private void loadRoomData() {
        new Thread(() -> {
            try {
                currentRoom = database.studyRoomDao().getRoomById(roomId);

                runOnUiThread(() -> {
                    if (currentRoom != null) {
                        displayRoomInfo();
                        loadSeatsData();
                    } else {
                        Toast.makeText(this, "房间不存在", Toast.LENGTH_SHORT).show();
                        finish();
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载房间信息失败", Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        }).start();
    }

    private void displayRoomInfo() {
        tvRoomName.setText(currentRoom.getRoomName());
        tvRoomInfo.setText(String.format("%s | 容量: %d人",
            currentRoom.getLocation(), currentRoom.getCapacity()));
        tvRoomFacilities.setText("设施: " + currentRoom.getFacilities());
    }

    private void loadSeatsData() {
        new Thread(() -> {
            try {
                // 获取房间所有座位
                roomSeats = database.seatDao().getSeatsByRoom(roomId);

                // 获取指定日期的现有预约
                existingReservations = database.seatReservationDao()
                    .getReservationsForDateAndTimeRange(selectedDate, selectedStartTime, selectedEndTime);

                // 在Java中过滤时间冲突的预约
                List<SeatReservation> filteredReservations = new ArrayList<>();
                for (SeatReservation reservation : existingReservations) {
                    if (SeatUtils.hasTimeConflict(selectedStartTime, selectedEndTime,
                                                 reservation.getStartTime(), reservation.getEndTime())) {
                        filteredReservations.add(reservation);
                    }
                }
                existingReservations = filteredReservations;

                // 更新座位状态
                updateSeatStatus();

                runOnUiThread(() -> {
                    seatGridView.setSeats(roomSeats);
                    selectedSeat = null;
                    tvSelectedSeat.setText("未选择");
                    updateReserveButton();
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载座位信息失败", Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void updateSeatStatus() {
        for (Seat seat : roomSeats) {
            // 检查座位是否在指定时间段被预约
            boolean isOccupied = false;
            for (SeatReservation reservation : existingReservations) {
                if (reservation.getSeatId() == seat.getSeatId() &&
                    SeatUtils.isReservationActive(reservation)) {
                    isOccupied = true;
                    break;
                }
            }

            if (isOccupied) {
                seat.setStatus(SeatUtils.STATUS_OCCUPIED);
            } else if (seat.getStatus().equals(SeatUtils.STATUS_OCCUPIED)) {
                // 如果之前是占用状态，现在没有预约，则恢复为可用
                seat.setStatus(SeatUtils.STATUS_AVAILABLE);
            }
        }
    }

    private void updateReserveButton() {
        boolean canReserve = selectedSeat != null &&
                           SeatUtils.isSeatAvailable(selectedSeat) &&
                           SeatUtils.isValidReservationDuration(selectedStartTime, selectedEndTime);

        btnReserve.setEnabled(canReserve);
    }

    private void performReservation() {
        if (selectedSeat == null) {
            Toast.makeText(this, "请选择座位", Toast.LENGTH_SHORT).show();
            return;
        }

        if (!SeatUtils.isValidReservationDuration(selectedStartTime, selectedEndTime)) {
            Toast.makeText(this, "预约时长不合理", Toast.LENGTH_SHORT).show();
            return;
        }

        btnReserve.setEnabled(false);
        btnReserve.setText("预约中...");

        new Thread(() -> {
            try {
                int userId = prefsManager.getCurrentUserId();

                // 再次检查座位是否可用（防止并发预约）
                List<SeatReservation> allReservations = database.seatReservationDao()
                    .getConflictingReservations(selectedSeat.getSeatId(), selectedDate,
                                              selectedStartTime, selectedEndTime);

                // 在Java中检查时间冲突
                List<SeatReservation> conflicts = new ArrayList<>();
                for (SeatReservation reservation : allReservations) {
                    if (SeatUtils.hasTimeConflict(selectedStartTime, selectedEndTime,
                                                 reservation.getStartTime(), reservation.getEndTime())) {
                        conflicts.add(reservation);
                    }
                }

                if (!conflicts.isEmpty()) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "座位已被预约，请选择其他座位", Toast.LENGTH_SHORT).show();
                        btnReserve.setEnabled(true);
                        btnReserve.setText("确认预约");
                        loadSeatsData(); // 刷新座位状态
                    });
                    return;
                }

                // 创建预约记录
                SeatReservation reservation = new SeatReservation();
                reservation.setUserId(userId);
                reservation.setSeatId(selectedSeat.getSeatId());
                reservation.setReservationDate(selectedDate);
                reservation.setStartTime(selectedStartTime);
                reservation.setEndTime(selectedEndTime);
                reservation.setStatus(SeatUtils.RESERVATION_ACTIVE);

                long reservationId = database.seatReservationDao().insertReservation(reservation);

                runOnUiThread(() -> {
                    if (reservationId > 0) {
                        Toast.makeText(this, "预约成功！", Toast.LENGTH_LONG).show();
                        finish();
                    } else {
                        Toast.makeText(this, "预约失败，请重试", Toast.LENGTH_SHORT).show();
                        btnReserve.setEnabled(true);
                        btnReserve.setText("确认预约");
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "预约失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                    btnReserve.setEnabled(true);
                    btnReserve.setText("确认预约");
                });
            }
        }).start();
    }

    private void showRoomSelection() {
        new Thread(() -> {
            try {
                // 获取所有可用房间
                List<StudyRoom> availableRooms = database.studyRoomDao().getAllAvailableRooms();

                runOnUiThread(() -> {
                    if (availableRooms.isEmpty()) {
                        Toast.makeText(this, "暂无可用房间", Toast.LENGTH_SHORT).show();
                        finish();
                        return;
                    }

                    // 如果只有一个房间，直接使用
                    if (availableRooms.size() == 1) {
                        roomId = availableRooms.get(0).getRoomId();
                        initViews();
                        initData();
                        setListeners();
                        loadRoomData();
                        return;
                    }

                    // 多个房间时显示选择对话框
                    showRoomSelectionDialog(availableRooms);
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载房间列表失败", Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        }).start();
    }

    private void showRoomSelectionDialog(List<StudyRoom> rooms) {
        String[] roomNames = new String[rooms.size()];
        for (int i = 0; i < rooms.size(); i++) {
            StudyRoom room = rooms.get(i);
            roomNames[i] = room.getRoomName() + " (" + room.getLocation() + ")";
        }

        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("选择学习室");
        builder.setItems(roomNames, (dialog, which) -> {
            roomId = rooms.get(which).getRoomId();
            initViews();
            initData();
            setListeners();
            loadRoomData();
        });
        builder.setOnCancelListener(dialog -> finish());
        builder.show();
    }
}
