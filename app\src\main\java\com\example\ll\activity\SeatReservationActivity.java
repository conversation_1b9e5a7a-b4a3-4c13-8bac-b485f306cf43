package com.example.ll.activity;

import android.os.Bundle;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.ll.R;

public class SeatReservationActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 获取传递的房间ID
        int roomId = getIntent().getIntExtra("room_id", -1);
        
        Toast.makeText(this, "座位预约页面开发中，房间ID: " + roomId, Toast.LENGTH_SHORT).show();
        
        // 暂时直接返回
        finish();
    }
}
