package com.example.ll.utils;

import android.content.Context;

import com.example.ll.model.User;

public class PermissionManager {
    
    // 权限常量
    public static final String PERMISSION_USER_MANAGEMENT = "user_management";
    public static final String PERMISSION_BOOK_MANAGEMENT = "book_management";
    public static final String PERMISSION_BORROWING_MANAGEMENT = "borrowing_management";
    public static final String PERMISSION_SEAT_MANAGEMENT = "seat_management";
    public static final String PERMISSION_SYSTEM_SETTINGS = "system_settings";
    public static final String PERMISSION_DATA_EXPORT = "data_export";
    public static final String PERMISSION_STATISTICS_VIEW = "statistics_view";
    
    private static PermissionManager instance;
    private SharedPreferencesManager prefsManager;
    
    private PermissionManager(Context context) {
        prefsManager = SharedPreferencesManager.getInstance(context);
    }
    
    public static synchronized PermissionManager getInstance(Context context) {
        if (instance == null) {
            instance = new PermissionManager(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 检查当前用户是否有指定权限
     */
    public boolean hasPermission(String permission) {
        // 如果是管理员，拥有所有权限
        if (prefsManager.isAdmin()) {
            return true;
        }
        
        // 根据用户角色检查权限
        String userRole = getCurrentUserRole();
        return hasRolePermission(userRole, permission);
    }
    
    /**
     * 检查指定用户是否有指定权限
     */
    public boolean hasPermission(User user, String permission) {
        if (user == null) {
            return false;
        }
        
        // 如果是管理员，拥有所有权限
        if (UserUtils.isAdmin(user)) {
            return true;
        }
        
        // 根据用户角色检查权限
        return hasRolePermission(user.getRole(), permission);
    }
    
    /**
     * 检查角色是否有指定权限
     */
    private boolean hasRolePermission(String role, String permission) {
        switch (role) {
            case UserUtils.ROLE_ADMIN:
                return true; // 管理员拥有所有权限
                
            case UserUtils.ROLE_LIBRARIAN:
                return isLibrarianPermission(permission);
                
            case UserUtils.ROLE_USER:
                return isUserPermission(permission);
                
            default:
                return false;
        }
    }
    
    /**
     * 检查是否是图书管理员权限
     */
    private boolean isLibrarianPermission(String permission) {
        switch (permission) {
            case PERMISSION_BOOK_MANAGEMENT:
            case PERMISSION_BORROWING_MANAGEMENT:
            case PERMISSION_SEAT_MANAGEMENT:
            case PERMISSION_STATISTICS_VIEW:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 检查是否是普通用户权限
     */
    private boolean isUserPermission(String permission) {
        // 普通用户没有管理权限
        return false;
    }
    
    /**
     * 获取当前用户角色
     */
    private String getCurrentUserRole() {
        if (prefsManager.isAdmin()) {
            return UserUtils.ROLE_ADMIN;
        }
        // 这里可以从数据库获取用户角色，暂时返回普通用户
        return UserUtils.ROLE_USER;
    }
    
    /**
     * 检查用户管理权限
     */
    public boolean canManageUsers() {
        return hasPermission(PERMISSION_USER_MANAGEMENT);
    }
    
    /**
     * 检查图书管理权限
     */
    public boolean canManageBooks() {
        return hasPermission(PERMISSION_BOOK_MANAGEMENT);
    }
    
    /**
     * 检查借阅管理权限
     */
    public boolean canManageBorrowing() {
        return hasPermission(PERMISSION_BORROWING_MANAGEMENT);
    }
    
    /**
     * 检查座位管理权限
     */
    public boolean canManageSeats() {
        return hasPermission(PERMISSION_SEAT_MANAGEMENT);
    }
    
    /**
     * 检查系统设置权限
     */
    public boolean canManageSystem() {
        return hasPermission(PERMISSION_SYSTEM_SETTINGS);
    }
    
    /**
     * 检查数据导出权限
     */
    public boolean canExportData() {
        return hasPermission(PERMISSION_DATA_EXPORT);
    }
    
    /**
     * 检查统计查看权限
     */
    public boolean canViewStatistics() {
        return hasPermission(PERMISSION_STATISTICS_VIEW);
    }
    
    /**
     * 检查是否可以编辑指定用户
     */
    public boolean canEditUser(User targetUser) {
        if (targetUser == null) {
            return false;
        }
        
        // 管理员可以编辑所有用户
        if (hasPermission(PERMISSION_USER_MANAGEMENT)) {
            return true;
        }
        
        // 用户只能编辑自己
        int currentUserId = prefsManager.getCurrentUserId();
        return currentUserId == targetUser.getUserId();
    }
    
    /**
     * 检查是否可以删除指定用户
     */
    public boolean canDeleteUser(User targetUser) {
        if (targetUser == null) {
            return false;
        }
        
        // 只有管理员可以删除用户
        if (!hasPermission(PERMISSION_USER_MANAGEMENT)) {
            return false;
        }
        
        // 不能删除自己
        int currentUserId = prefsManager.getCurrentUserId();
        if (currentUserId == targetUser.getUserId()) {
            return false;
        }
        
        // 不能删除其他管理员（除非是超级管理员）
        if (UserUtils.isAdmin(targetUser)) {
            return prefsManager.getCurrentUserId() == 999; // 超级管理员ID
        }
        
        return true;
    }
    
    /**
     * 检查是否可以冻结/解冻指定用户
     */
    public boolean canFreezeUser(User targetUser) {
        if (targetUser == null) {
            return false;
        }
        
        // 只有管理员可以冻结用户
        if (!hasPermission(PERMISSION_USER_MANAGEMENT)) {
            return false;
        }
        
        // 不能冻结自己
        int currentUserId = prefsManager.getCurrentUserId();
        if (currentUserId == targetUser.getUserId()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查是否可以修改用户角色
     */
    public boolean canChangeUserRole(User targetUser) {
        if (targetUser == null) {
            return false;
        }
        
        // 只有管理员可以修改用户角色
        if (!hasPermission(PERMISSION_USER_MANAGEMENT)) {
            return false;
        }
        
        // 不能修改自己的角色
        int currentUserId = prefsManager.getCurrentUserId();
        if (currentUserId == targetUser.getUserId()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取权限描述
     */
    public static String getPermissionDescription(String permission) {
        switch (permission) {
            case PERMISSION_USER_MANAGEMENT:
                return "用户管理";
            case PERMISSION_BOOK_MANAGEMENT:
                return "图书管理";
            case PERMISSION_BORROWING_MANAGEMENT:
                return "借阅管理";
            case PERMISSION_SEAT_MANAGEMENT:
                return "座位管理";
            case PERMISSION_SYSTEM_SETTINGS:
                return "系统设置";
            case PERMISSION_DATA_EXPORT:
                return "数据导出";
            case PERMISSION_STATISTICS_VIEW:
                return "统计查看";
            default:
                return "未知权限";
        }
    }
    
    /**
     * 获取角色的所有权限
     */
    public static String[] getRolePermissions(String role) {
        switch (role) {
            case UserUtils.ROLE_ADMIN:
                return new String[]{
                    PERMISSION_USER_MANAGEMENT,
                    PERMISSION_BOOK_MANAGEMENT,
                    PERMISSION_BORROWING_MANAGEMENT,
                    PERMISSION_SEAT_MANAGEMENT,
                    PERMISSION_SYSTEM_SETTINGS,
                    PERMISSION_DATA_EXPORT,
                    PERMISSION_STATISTICS_VIEW
                };
                
            case UserUtils.ROLE_LIBRARIAN:
                return new String[]{
                    PERMISSION_BOOK_MANAGEMENT,
                    PERMISSION_BORROWING_MANAGEMENT,
                    PERMISSION_SEAT_MANAGEMENT,
                    PERMISSION_STATISTICS_VIEW
                };
                
            case UserUtils.ROLE_USER:
                return new String[]{};
                
            default:
                return new String[]{};
        }
    }
}
