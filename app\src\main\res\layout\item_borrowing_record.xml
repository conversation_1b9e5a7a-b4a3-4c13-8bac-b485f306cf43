<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_borrowing"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 图书信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_book_cover"
                android:layout_width="60dp"
                android:layout_height="80dp"
                android:scaleType="centerCrop"
                android:background="@color/background_color"
                android:contentDescription="图书封面" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="16dp">

                <TextView
                    android:id="@+id/tv_book_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="图书标题"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_book_author"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="作者"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:layout_marginTop="4dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="8dp">

                    <TextView
                        android:id="@+id/tv_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="正常"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color"
                        android:background="@drawable/bg_status_normal"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="2dp" />

                    <TextView
                        android:id="@+id/tv_days_remaining"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="还剩 15 天"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginStart="8dp"
                        android:layout_gravity="center_vertical" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color"
            android:layout_marginVertical="16dp" />

        <!-- 日期信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_borrow_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="借阅日期：01月15日"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tv_due_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="应还日期：02月14日"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:gravity="end" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/btn_renew"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="续借 (0/2)"
                android:textSize="14sp"
                android:background="@drawable/button_outline"
                android:textColor="@color/primary_color"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_return"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="归还"
                android:textSize="14sp"
                android:background="@drawable/button_primary"
                android:textColor="@android:color/white"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
