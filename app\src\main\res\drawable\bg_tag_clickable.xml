<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/primary_color">

    <item>
        <selector>
            <!-- 按下状态 -->
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <solid android:color="@color/primary_color" />
                    <corners android:radius="16dp" />
                    <stroke
                        android:width="1dp"
                        android:color="@color/primary_color" />
                </shape>
            </item>

            <!-- 获得焦点状态 -->
            <item android:state_focused="true">
                <shape android:shape="rectangle">
                    <solid android:color="@color/primary_light" />
                    <corners android:radius="16dp" />
                    <stroke
                        android:width="2dp"
                        android:color="@color/primary_color" />
                </shape>
            </item>

            <!-- 默认状态 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/primary_light" />
                    <corners android:radius="16dp" />
                    <stroke
                        android:width="1dp"
                        android:color="@color/primary_color" />
                </shape>
            </item>
        </selector>
    </item>

</ripple>
