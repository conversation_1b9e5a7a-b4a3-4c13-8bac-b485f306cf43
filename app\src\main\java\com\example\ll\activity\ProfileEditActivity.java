package com.example.ll.activity;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.ll.R;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.User;
import com.example.ll.utils.PermissionUtils;
import com.example.ll.utils.SharedPreferencesManager;
import com.example.ll.utils.UserUtils;

import java.util.Calendar;

public class ProfileEditActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private ImageView ivAvatar;
    private EditText etNickname, etRealName, etEmail, etPhone, etStudentId, etAddress, etBio;
    private TextView tvBirthday;
    private RadioGroup rgGender;
    private RadioButton rbMale, rbFemale, rbOther;
    private Button btnSave;
    
    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private User currentUser;
    
    private static final int REQUEST_IMAGE_PICK = 1001;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_profile_edit);
        
        initViews();
        initData();
        setListeners();
        loadUserData();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        ivAvatar = findViewById(R.id.iv_avatar);
        etNickname = findViewById(R.id.et_nickname);
        etRealName = findViewById(R.id.et_real_name);
        etEmail = findViewById(R.id.et_email);
        etPhone = findViewById(R.id.et_phone);
        etStudentId = findViewById(R.id.et_student_id);
        etAddress = findViewById(R.id.et_address);
        etBio = findViewById(R.id.et_bio);
        tvBirthday = findViewById(R.id.tv_birthday);
        rgGender = findViewById(R.id.rg_gender);
        rbMale = findViewById(R.id.rb_male);
        rbFemale = findViewById(R.id.rb_female);
        rbOther = findViewById(R.id.rb_other);
        btnSave = findViewById(R.id.btn_save);
        
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("编辑个人信息");
        }
    }
    
    private void initData() {
        database = LibraryDatabase.getInstance(this);
        prefsManager = SharedPreferencesManager.getInstance(this);
    }
    
    private void setListeners() {
        toolbar.setNavigationOnClickListener(v -> finish());
        
        ivAvatar.setOnClickListener(v -> selectAvatar());
        
        tvBirthday.setOnClickListener(v -> showDatePicker());
        
        btnSave.setOnClickListener(v -> saveUserInfo());
    }
    
    private void loadUserData() {
        new Thread(() -> {
            try {
                int userId = prefsManager.getCurrentUserId();
                currentUser = database.userDao().getUserById(userId);
                
                runOnUiThread(() -> {
                    if (currentUser != null) {
                        displayUserInfo();
                    } else {
                        Toast.makeText(this, "用户信息加载失败", Toast.LENGTH_SHORT).show();
                        finish();
                    }
                });
                
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载用户信息失败", Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        }).start();
    }
    
    private void displayUserInfo() {
        if (!TextUtils.isEmpty(currentUser.getNickname())) {
            etNickname.setText(currentUser.getNickname());
        }
        
        if (!TextUtils.isEmpty(currentUser.getRealName())) {
            etRealName.setText(currentUser.getRealName());
        }
        
        if (!TextUtils.isEmpty(currentUser.getEmail())) {
            etEmail.setText(currentUser.getEmail());
        }
        
        if (!TextUtils.isEmpty(currentUser.getPhone())) {
            etPhone.setText(currentUser.getPhone());
        }
        
        if (!TextUtils.isEmpty(currentUser.getStudentId())) {
            etStudentId.setText(currentUser.getStudentId());
        }
        
        if (!TextUtils.isEmpty(currentUser.getAddress())) {
            etAddress.setText(currentUser.getAddress());
        }
        
        if (!TextUtils.isEmpty(currentUser.getBio())) {
            etBio.setText(currentUser.getBio());
        }
        
        if (!TextUtils.isEmpty(currentUser.getBirthday())) {
            tvBirthday.setText(currentUser.getBirthday());
        }
        
        // 设置性别
        String gender = currentUser.getGender();
        if (UserUtils.GENDER_MALE.equals(gender)) {
            rbMale.setChecked(true);
        } else if (UserUtils.GENDER_FEMALE.equals(gender)) {
            rbFemale.setChecked(true);
        } else if (UserUtils.GENDER_OTHER.equals(gender)) {
            rbOther.setChecked(true);
        }
        
        // 加载头像
        loadAvatar();
    }
    
    private void loadAvatar() {
        // TODO: 实现头像加载逻辑
        // 这里可以使用Glide或其他图片加载库
    }
    
    private void selectAvatar() {
        // 检查设备是否有相机硬件
        boolean hasCamera = PermissionUtils.hasCameraHardware(this);
        boolean hasCameraPermission = PermissionUtils.hasCameraPermission(this);

        if (hasCamera && hasCameraPermission) {
            // 设备有相机且有权限，显示选择对话框
            showAvatarSelectionDialog();
        } else if (hasCamera && !hasCameraPermission) {
            // 设备有相机但没有权限，请求权限
            PermissionUtils.requestCameraPermission(this);
        } else {
            // 设备没有相机，只能从相册选择
            selectFromGallery();
        }
    }

    private void showAvatarSelectionDialog() {
        String[] options = {"拍照", "从相册选择"};

        new AlertDialog.Builder(this)
            .setTitle("选择头像")
            .setItems(options, (dialog, which) -> {
                if (which == 0) {
                    // 拍照
                    capturePhoto();
                } else {
                    // 从相册选择
                    selectFromGallery();
                }
            })
            .show();
    }

    private void capturePhoto() {
        // TODO: 实现拍照功能
        Toast.makeText(this, "拍照功能开发中", Toast.LENGTH_SHORT).show();
    }

    private void selectFromGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setType("image/*");
        startActivityForResult(intent, REQUEST_IMAGE_PICK);
    }
    
    private void showDatePicker() {
        Calendar calendar = Calendar.getInstance();
        
        // 如果已有生日，解析并设置为默认值
        if (!TextUtils.isEmpty(currentUser.getBirthday())) {
            try {
                String[] dateParts = currentUser.getBirthday().split("-");
                if (dateParts.length == 3) {
                    calendar.set(Integer.parseInt(dateParts[0]), 
                               Integer.parseInt(dateParts[1]) - 1, 
                               Integer.parseInt(dateParts[2]));
                }
            } catch (Exception e) {
                // 解析失败，使用当前日期
            }
        }
        
        DatePickerDialog dialog = new DatePickerDialog(this, 
            (view, year, month, dayOfMonth) -> {
                String birthday = String.format("%04d-%02d-%02d", year, month + 1, dayOfMonth);
                tvBirthday.setText(birthday);
            }, 
            calendar.get(Calendar.YEAR), 
            calendar.get(Calendar.MONTH), 
            calendar.get(Calendar.DAY_OF_MONTH));
        
        dialog.show();
    }
    
    private void saveUserInfo() {
        final String nickname = etNickname.getText().toString().trim();
        final String realName = etRealName.getText().toString().trim();
        final String email = etEmail.getText().toString().trim();
        final String phone = etPhone.getText().toString().trim();
        final String studentId = etStudentId.getText().toString().trim();
        final String address = etAddress.getText().toString().trim();
        final String bio = etBio.getText().toString().trim();
        final String birthday = tvBirthday.getText().toString().trim();
        
        // 验证输入
        if (!TextUtils.isEmpty(email) && !UserUtils.isValidEmail(email)) {
            etEmail.setError("邮箱格式不正确");
            return;
        }
        
        if (!TextUtils.isEmpty(phone) && !UserUtils.isValidPhone(phone)) {
            etPhone.setError("手机号格式不正确");
            return;
        }
        
        if (!TextUtils.isEmpty(studentId) && !UserUtils.isValidStudentId(studentId)) {
            etStudentId.setError("学号格式不正确");
            return;
        }
        
        // 获取性别
        final String gender;
        int checkedId = rgGender.getCheckedRadioButtonId();
        if (checkedId == R.id.rb_male) {
            gender = UserUtils.GENDER_MALE;
        } else if (checkedId == R.id.rb_female) {
            gender = UserUtils.GENDER_FEMALE;
        } else if (checkedId == R.id.rb_other) {
            gender = UserUtils.GENDER_OTHER;
        } else {
            gender = null;
        }
        
        btnSave.setEnabled(false);
        btnSave.setText("保存中...");
        
        // 保存用户信息
        new Thread(() -> {
            try {
                // 检查邮箱和手机号是否被其他用户使用
                if (!TextUtils.isEmpty(email) && !email.equals(currentUser.getEmail())) {
                    User existingEmailUser = database.userDao().getUserByEmail(email);
                    if (existingEmailUser != null && existingEmailUser.getUserId() != currentUser.getUserId()) {
                        runOnUiThread(() -> {
                            etEmail.setError("邮箱已被其他用户使用");
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                        });
                        return;
                    }
                }
                
                if (!TextUtils.isEmpty(phone) && !phone.equals(currentUser.getPhone())) {
                    User existingPhoneUser = database.userDao().getUserByPhone(phone);
                    if (existingPhoneUser != null && existingPhoneUser.getUserId() != currentUser.getUserId()) {
                        runOnUiThread(() -> {
                            etPhone.setError("手机号已被其他用户使用");
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                        });
                        return;
                    }
                }
                
                // 更新用户信息
                currentUser.setNickname(nickname);
                currentUser.setRealName(realName);
                currentUser.setEmail(email);
                currentUser.setPhone(phone);
                currentUser.setStudentId(studentId);
                currentUser.setAddress(address);
                currentUser.setBio(bio);
                currentUser.setBirthday(birthday);
                currentUser.setGender(gender);
                currentUser.setUpdatedAt(System.currentTimeMillis());
                
                database.userDao().updateUser(currentUser);
                
                runOnUiThread(() -> {
                    Toast.makeText(this, "个人信息保存成功", Toast.LENGTH_SHORT).show();
                    
                    // 更新SharedPreferences中的用户信息
                    String displayName = UserUtils.getDisplayName(currentUser);
                    prefsManager.saveUserLogin(currentUser.getUserId(), currentUser.getUsername(), 
                                             displayName, UserUtils.hasAdminPermission(currentUser));
                    
                    finish();
                });
                
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "保存失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                    btnSave.setEnabled(true);
                    btnSave.setText("保存");
                });
            }
        }).start();
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PermissionUtils.REQUEST_CAMERA) {
            if (PermissionUtils.handlePermissionResult(requestCode, permissions, grantResults)) {
                // 相机权限授予成功，显示选择对话框
                showAvatarSelectionDialog();
            } else {
                // 相机权限被拒绝，只能从相册选择
                Toast.makeText(this, "相机权限被拒绝，只能从相册选择图片", Toast.LENGTH_SHORT).show();
                selectFromGallery();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_IMAGE_PICK && resultCode == RESULT_OK && data != null) {
            Uri imageUri = data.getData();
            if (imageUri != null) {
                // TODO: 处理头像上传
                // 这里可以实现头像上传到服务器或本地存储的逻辑
                ivAvatar.setImageURI(imageUri);
            }
        }
    }
}
