package com.example.ll.database;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

/**
 * 备用数据库助手类 - 如果Room无法正常工作时使用
 */
public class DatabaseHelper extends SQLiteOpenHelper {
    
    private static final String DATABASE_NAME = "library.db";
    private static final int DATABASE_VERSION = 1;
    
    // 用户表
    private static final String CREATE_USERS_TABLE = 
        "CREATE TABLE users (" +
        "user_id INTEGER PRIMARY KEY AUTOINCREMENT," +
        "username TEXT UNIQUE NOT NULL," +
        "password TEXT NOT NULL," +
        "email TEXT," +
        "phone TEXT," +
        "nickname TEXT," +
        "real_name TEXT," +
        "student_id TEXT," +
        "status TEXT DEFAULT 'active'," +
        "created_at INTEGER," +
        "updated_at INTEGER" +
        ")";
    
    // 图书分类表
    private static final String CREATE_CATEGORIES_TABLE = 
        "CREATE TABLE book_categories (" +
        "category_id INTEGER PRIMARY KEY AUTOINCREMENT," +
        "category_name TEXT NOT NULL," +
        "parent_id INTEGER," +
        "description TEXT," +
        "sort_order INTEGER DEFAULT 0," +
        "created_at INTEGER" +
        ")";
    
    // 图书表
    private static final String CREATE_BOOKS_TABLE = 
        "CREATE TABLE books (" +
        "book_id INTEGER PRIMARY KEY AUTOINCREMENT," +
        "isbn TEXT," +
        "title TEXT NOT NULL," +
        "author TEXT," +
        "publisher TEXT," +
        "publish_date TEXT," +
        "category_id INTEGER," +
        "description TEXT," +
        "cover_image TEXT," +
        "total_copies INTEGER DEFAULT 1," +
        "available_copies INTEGER DEFAULT 1," +
        "location TEXT," +
        "price REAL," +
        "status TEXT DEFAULT 'available'," +
        "created_at INTEGER," +
        "updated_at INTEGER" +
        ")";
    
    // 借阅记录表
    private static final String CREATE_BORROWING_TABLE = 
        "CREATE TABLE borrowing_records (" +
        "record_id INTEGER PRIMARY KEY AUTOINCREMENT," +
        "user_id INTEGER NOT NULL," +
        "book_id INTEGER NOT NULL," +
        "borrow_date INTEGER," +
        "due_date INTEGER," +
        "return_date INTEGER," +
        "renewal_count INTEGER DEFAULT 0," +
        "status TEXT DEFAULT 'borrowed'," +
        "fine_amount REAL DEFAULT 0.0," +
        "admin_id INTEGER," +
        "notes TEXT," +
        "created_at INTEGER," +
        "updated_at INTEGER" +
        ")";
    
    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(CREATE_USERS_TABLE);
        db.execSQL(CREATE_CATEGORIES_TABLE);
        db.execSQL(CREATE_BOOKS_TABLE);
        db.execSQL(CREATE_BORROWING_TABLE);
        
        // 插入初始数据
        insertInitialData(db);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS users");
        db.execSQL("DROP TABLE IF EXISTS book_categories");
        db.execSQL("DROP TABLE IF EXISTS books");
        db.execSQL("DROP TABLE IF EXISTS borrowing_records");
        onCreate(db);
    }
    
    private void insertInitialData(SQLiteDatabase db) {
        // 插入图书分类
        db.execSQL("INSERT INTO book_categories (category_name, description) VALUES ('文学', '文学类图书')");
        db.execSQL("INSERT INTO book_categories (category_name, description) VALUES ('科技', '科技类图书')");
        db.execSQL("INSERT INTO book_categories (category_name, description) VALUES ('历史', '历史类图书')");
        db.execSQL("INSERT INTO book_categories (category_name, description) VALUES ('艺术', '艺术类图书')");
        
        // 插入示例图书
        long currentTime = System.currentTimeMillis();
        db.execSQL("INSERT INTO books (title, author, isbn, category_id, total_copies, available_copies, created_at, updated_at) " +
                   "VALUES ('红楼梦', '曹雪芹', '9787020002207', 1, 5, 5, " + currentTime + ", " + currentTime + ")");
        db.execSQL("INSERT INTO books (title, author, isbn, category_id, total_copies, available_copies, created_at, updated_at) " +
                   "VALUES ('Java编程思想', 'Bruce Eckel', '9787111213826', 2, 8, 8, " + currentTime + ", " + currentTime + ")");
    }
}
