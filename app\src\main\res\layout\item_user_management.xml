<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 状态指示器 -->
        <View
            android:id="@+id/status_indicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="12dp"
            android:background="@color/success_color" />

        <!-- 头像 -->
        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/avatar_default"
            android:scaleType="centerCrop"
            android:background="@drawable/bg_circle"
            android:layout_marginEnd="12dp" />

        <!-- 用户信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 第一行：显示名称和角色 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_display_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="用户名称"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_role"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="普通用户"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_secondary"
                    android:background="@drawable/bg_role_tag"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- 第二行：用户名和状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/tv_username"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@username"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="正常"
                    android:textSize="12sp"
                    android:textColor="@color/success_color"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- 第三行：联系信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/tv_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="12sp"
                    android:textColor="@color/text_hint"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:drawableStart="@drawable/ic_email_small"
                    android:drawablePadding="4dp"
                    android:gravity="center_vertical" />

                <TextView
                    android:id="@+id/tv_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="13800138000"
                    android:textSize="12sp"
                    android:textColor="@color/text_hint"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:drawableStart="@drawable/ic_phone_small"
                    android:drawablePadding="4dp"
                    android:gravity="center_vertical"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <!-- 第四行：时间信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/tv_created_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="注册：2024-01-01"
                    android:textSize="11sp"
                    android:textColor="@color/text_hint"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_last_login"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="最后登录：2024-01-01 10:00"
                    android:textSize="11sp"
                    android:textColor="@color/text_hint"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:layout_marginTop="1dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="8dp"
            android:gravity="center">

            <ImageView
                android:id="@+id/iv_edit"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_edit"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:contentDescription="编辑用户"
                app:tint="@color/primary_color" />

            <ImageView
                android:id="@+id/iv_freeze"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_freeze"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:contentDescription="冻结用户"
                android:layout_marginTop="4dp"
                app:tint="@color/warning_dark" />

            <ImageView
                android:id="@+id/iv_delete"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_delete"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:contentDescription="删除用户"
                android:layout_marginTop="4dp"
                app:tint="@color/error_color" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
