package com.example.ll.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.ll.R;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.User;
import com.example.ll.utils.SharedPreferencesManager;

public class LoginActivity extends AppCompatActivity {

    private EditText etUsername, etPassword;
    private CheckBox cbRememberPassword;
    private Button btnLogin;
    private TextView tvRegister, tvAdminLogin;
    
    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        
        initViews();
        initData();
        setListeners();
    }

    private void initViews() {
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        cbRememberPassword = findViewById(R.id.cb_remember_password);
        btnLogin = findViewById(R.id.btn_login);
        tvRegister = findViewById(R.id.tv_register);
        tvAdminLogin = findViewById(R.id.tv_admin_login);
    }

    private void initData() {
        database = LibraryDatabase.getInstance(this);
        prefsManager = SharedPreferencesManager.getInstance(this);
        
        // 如果记住密码，自动填充
        if (prefsManager.isRememberPassword()) {
            etUsername.setText(prefsManager.getCurrentUsername());
            etPassword.setText(prefsManager.getSavedPassword());
            cbRememberPassword.setChecked(true);
        }
    }

    private void setListeners() {
        btnLogin.setOnClickListener(v -> performLogin());
        
        tvRegister.setOnClickListener(v -> {
            Intent intent = new Intent(LoginActivity.this, RegisterActivity.class);
            startActivity(intent);
        });
        
        tvAdminLogin.setOnClickListener(v -> {
            Intent intent = new Intent(LoginActivity.this, AdminLoginActivity.class);
            startActivity(intent);
        });
    }

    private void performLogin() {
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        
        if (TextUtils.isEmpty(username)) {
            etUsername.setError("请输入用户名");
            etUsername.requestFocus();
            return;
        }
        
        if (TextUtils.isEmpty(password)) {
            etPassword.setError("请输入密码");
            etPassword.requestFocus();
            return;
        }
        
        // 在后台线程中执行登录
        new Thread(() -> {
            try {
                User user = database.userDao().loginWithAccount(username, password);
                
                runOnUiThread(() -> {
                    if (user != null && "active".equals(user.getStatus())) {
                        // 登录成功
                        String nickname = TextUtils.isEmpty(user.getNickname()) ? user.getUsername() : user.getNickname();
                        prefsManager.saveUserLogin(user.getUserId(), user.getUsername(), nickname, false);
                        
                        // 保存密码记住状态
                        prefsManager.savePasswordRemember(cbRememberPassword.isChecked(), password);
                        
                        Toast.makeText(this, "登录成功，欢迎 " + nickname, Toast.LENGTH_SHORT).show();
                        
                        // 跳转到主界面
                        Intent intent = new Intent(LoginActivity.this, UserMainActivity.class);
                        startActivity(intent);
                        finish();
                        
                    } else if (user != null && "frozen".equals(user.getStatus())) {
                        Toast.makeText(this, "账号已被冻结，请联系管理员", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(this, "用户名或密码错误", Toast.LENGTH_SHORT).show();
                    }
                });
                
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "登录失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }
}
