<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_reservation"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 房间和座位信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_room_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="自习室A"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/tv_seat_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="座位号：001"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="待签到"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/warning_color"
                android:background="@drawable/bg_status_warning"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:layout_gravity="center_vertical" />

        </LinearLayout>

        <!-- 时间信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp">

            <TextView
                android:id="@+id/tv_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="01月15日"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:drawableStart="@drawable/ic_date"
                android:drawablePadding="4dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="09:00 - 12:00"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:drawableStart="@drawable/ic_time"
                android:drawablePadding="4dp"
                android:gravity="center_vertical" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/btn_check_in"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="签到"
                android:textSize="14sp"
                android:background="@drawable/button_primary"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btn_check_out"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="签退"
                android:textSize="14sp"
                android:background="@drawable/button_primary"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="取消预约"
                android:textSize="14sp"
                android:background="@drawable/button_outline"
                android:textColor="@color/error_color"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
