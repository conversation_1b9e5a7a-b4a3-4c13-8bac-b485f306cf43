package com.example.ll.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.example.ll.R;
import com.example.ll.model.StudyRoom;

import java.util.List;

public class StudyRoomAdapter extends RecyclerView.Adapter<StudyRoomAdapter.RoomViewHolder> {

    private Context context;
    private List<StudyRoom> rooms;
    private OnRoomClickListener listener;

    public interface OnRoomClickListener {
        void onRoomClick(StudyRoom room);
    }

    public StudyRoomAdapter(Context context, List<StudyRoom> rooms) {
        this.context = context;
        this.rooms = rooms;
    }

    public void setOnRoomClickListener(OnRoomClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public RoomViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_study_room, parent, false);
        return new RoomViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RoomViewHolder holder, int position) {
        StudyRoom room = rooms.get(position);
        holder.bind(room);
    }

    @Override
    public int getItemCount() {
        return rooms.size();
    }

    public void updateRooms(List<StudyRoom> newRooms) {
        this.rooms.clear();
        this.rooms.addAll(newRooms);
        notifyDataSetChanged();
    }

    public class RoomViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private TextView tvRoomName, tvRoomType, tvCapacity, tvLocation;

        public RoomViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_room);
            tvRoomName = itemView.findViewById(R.id.tv_room_name);
            tvRoomType = itemView.findViewById(R.id.tv_room_type);
            tvCapacity = itemView.findViewById(R.id.tv_capacity);
            tvLocation = itemView.findViewById(R.id.tv_location);
        }

        public void bind(StudyRoom room) {
            tvRoomName.setText(room.getRoomName());
            tvRoomType.setText(getRoomTypeText(room.getRoomType()));
            tvCapacity.setText("容量：" + room.getCapacity() + "人");
            tvLocation.setText(room.getLocation());

            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onRoomClick(room);
                }
            });
        }

        private String getRoomTypeText(String roomType) {
            switch (roomType) {
                case "study_room":
                    return "自习室";
                case "reading_room":
                    return "阅览室";
                case "computer_room":
                    return "电子阅览室";
                default:
                    return "自习室";
            }
        }
    }
}
