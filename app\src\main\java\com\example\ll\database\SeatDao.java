package com.example.ll.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.ll.model.Seat;

import java.util.List;

@Dao
public interface SeatDao {
    
    @Insert
    long insertSeat(Seat seat);
    
    @Update
    void updateSeat(Seat seat);
    
    @Delete
    void deleteSeat(Seat seat);
    
    @Query("SELECT * FROM seats WHERE seatId = :seatId")
    Seat getSeatById(int seatId);
    
    @Query("SELECT * FROM seats WHERE roomId = :roomId ORDER BY seatNumber")
    List<Seat> getSeatsByRoom(int roomId);
    
    @Query("SELECT * FROM seats WHERE roomId = :roomId AND status = 'available' ORDER BY seatNumber")
    List<Seat> getAvailableSeatsByRoom(int roomId);
    
    @Query("SELECT * FROM seats WHERE status = 'available' ORDER BY roomId, seatNumber")
    List<Seat> getAllAvailableSeats();
    
    @Query("SELECT * FROM seats WHERE roomId = :roomId AND seatNumber = :seatNumber")
    Seat getSeatByRoomAndNumber(int roomId, String seatNumber);
    
    @Query("UPDATE seats SET status = :status WHERE seatId = :seatId")
    void updateSeatStatus(int seatId, String status);
    
    @Query("SELECT COUNT(*) FROM seats WHERE roomId = :roomId")
    int getSeatCountByRoom(int roomId);
    
    @Query("SELECT COUNT(*) FROM seats WHERE roomId = :roomId AND status = 'available'")
    int getAvailableSeatCountByRoom(int roomId);
    
    @Query("SELECT * FROM seats WHERE hasPower = 1 AND status = 'available' ORDER BY roomId, seatNumber")
    List<Seat> getSeatsWithPower();
    
    @Query("SELECT * FROM seats WHERE hasNetwork = 1 AND status = 'available' ORDER BY roomId, seatNumber")
    List<Seat> getSeatsWithNetwork();
}
