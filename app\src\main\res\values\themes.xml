<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.LL" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>
        <item name="colorSecondary">@color/accent_color</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>

    <style name="Theme.LL" parent="Base.Theme.LL" />

    <!-- 无ActionBar主题 -->
    <style name="Theme.LL.NoActionBar" parent="Theme.LL">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- 启动页主题 -->
    <style name="Theme.LL.Splash" parent="Theme.LL.NoActionBar">
        <item name="android:windowBackground">@color/primary_color</item>
        <item name="android:statusBarColor">@color/primary_color</item>
    </style>
</resources>