<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- 搜索框 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="0dp"
        app:cardElevation="4dp"
        android:layout_marginBottom="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_search"
                app:tint="@color/text_hint"
                android:layout_marginEnd="12dp" />

            <EditText
                android:id="@+id/et_search"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="搜索图书名称、作者、ISBN..."
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:background="@null"
                android:maxLines="1"
                android:imeOptions="actionSearch" />

            <ImageView
                android:id="@+id/iv_clear_search"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_clear"
                app:tint="@color/text_hint"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="2dp"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 内容区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 搜索提示 -->
        <ScrollView
            android:id="@+id/tv_search_hint"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="32dp"
                android:gravity="center_horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/ic_search_big"
                        app:tint="@color/text_hint"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="搜索图书"
                        android:textSize="18sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="输入图书名称、作者或ISBN进行搜索"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center"
                        android:layout_marginBottom="24dp" />

                </LinearLayout>

                <!-- 热门搜索区域 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="32dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="热门搜索"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp"
                        android:layout_gravity="center_horizontal" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_gravity="center_horizontal">

                        <TextView
                            android:id="@+id/tv_tag_java"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Java"
                            android:textSize="12sp"
                            android:textColor="@color/tag_text_color"
                            android:background="@drawable/bg_tag_clickable"
                            android:padding="12dp"
                            android:layout_marginEnd="8dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:minWidth="48dp"
                            android:minHeight="48dp"
                            android:gravity="center" />

                        <TextView
                            android:id="@+id/tv_tag_android"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Android"
                            android:textSize="12sp"
                            android:textColor="@color/tag_text_color"
                            android:background="@drawable/bg_tag_clickable"
                            android:padding="12dp"
                            android:layout_marginEnd="8dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:minWidth="48dp"
                            android:minHeight="48dp"
                            android:gravity="center" />

                        <TextView
                            android:id="@+id/tv_tag_literature"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="文学"
                            android:textSize="12sp"
                            android:textColor="@color/tag_text_color"
                            android:background="@drawable/bg_tag_clickable"
                            android:padding="12dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:minWidth="48dp"
                            android:minHeight="48dp"
                            android:gravity="center" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

        <!-- 搜索结果 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_search_results"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp"
                android:clipToPadding="false"
                android:visibility="gone" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <!-- 空状态 -->
        <LinearLayout
            android:id="@+id/tv_empty_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_empty_search"
                app:tint="@color/text_hint"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="未找到相关图书"
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请尝试其他关键词"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:gravity="center" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
