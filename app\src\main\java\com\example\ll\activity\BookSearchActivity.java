package com.example.ll.activity;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.ll.R;
import com.example.ll.adapter.BookAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Book;

import java.util.ArrayList;
import java.util.List;

public class BookSearchActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private EditText etSearch;
    private ImageView ivClearSearch;
    private RecyclerView rvSearchResults;
    private SwipeRefreshLayout swipeRefresh;
    private LinearLayout llEmptyView, llSearchHint;
    private TextView tvTagJava, tvTagAndroid, tvTagLiterature;

    private LibraryDatabase database;
    private BookAdapter bookAdapter;
    private List<Book> searchResults = new ArrayList<>();

    private String currentQuery = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_book_search);

        initViews();
        initData();
        setListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("图书搜索");

        etSearch = findViewById(R.id.et_search);
        ivClearSearch = findViewById(R.id.iv_clear_search);
        rvSearchResults = findViewById(R.id.rv_search_results);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        llEmptyView = findViewById(R.id.tv_empty_view);
        llSearchHint = findViewById(R.id.tv_search_hint);
        tvTagJava = findViewById(R.id.tv_tag_java);
        tvTagAndroid = findViewById(R.id.tv_tag_android);
        tvTagLiterature = findViewById(R.id.tv_tag_literature);

        rvSearchResults.setLayoutManager(new LinearLayoutManager(this));
    }

    private void initData() {
        database = LibraryDatabase.getInstance(this);

        bookAdapter = new BookAdapter(this, searchResults, false);
        rvSearchResults.setAdapter(bookAdapter);

        // 初始状态显示搜索提示
        showSearchHint();
    }

    private void setListeners() {
        toolbar.setNavigationOnClickListener(v -> finish());

        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String query = s.toString().trim();
                currentQuery = query;

                if (query.isEmpty()) {
                    ivClearSearch.setVisibility(View.GONE);
                    showSearchHint();
                } else {
                    ivClearSearch.setVisibility(View.VISIBLE);
                    performSearch(query);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        ivClearSearch.setOnClickListener(v -> {
            etSearch.setText("");
            etSearch.requestFocus();
        });

        swipeRefresh.setOnRefreshListener(() -> {
            if (!currentQuery.isEmpty()) {
                performSearch(currentQuery);
            } else {
                swipeRefresh.setRefreshing(false);
            }
        });

        // 热门标签点击事件
        tvTagJava.setOnClickListener(v -> {
            etSearch.setText("Java");
            performSearch("Java");
        });

        tvTagAndroid.setOnClickListener(v -> {
            etSearch.setText("Android");
            performSearch("Android");
        });

        tvTagLiterature.setOnClickListener(v -> {
            etSearch.setText("文学");
            performSearch("文学");
        });
    }

    private void performSearch(String query) {
        swipeRefresh.setRefreshing(true);
        hideAllViews();

        new Thread(() -> {
            try {
                // 从数据库搜索图书
                List<Book> results = database.bookDao().searchBooks("%" + query + "%");

                // 如果数据库中没有结果，使用模拟数据作为后备
                if (results.isEmpty()) {
                    results = createMockSearchResults(query);
                }

                runOnUiThread(() -> {
                    searchResults.clear();
                    searchResults.addAll(results);
                    bookAdapter.notifyDataSetChanged();

                    if (results.isEmpty()) {
                        showEmptyView();
                    } else {
                        showSearchResults();
                    }

                    swipeRefresh.setRefreshing(false);
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    // 如果数据库查询失败，使用模拟数据作为后备
                    List<Book> results = createMockSearchResults(query);
                    searchResults.clear();
                    searchResults.addAll(results);
                    bookAdapter.notifyDataSetChanged();

                    if (results.isEmpty()) {
                        showEmptyView();
                    } else {
                        showSearchResults();
                    }

                    swipeRefresh.setRefreshing(false);
                });
            }
        }).start();
    }

    private List<Book> createMockSearchResults(String query) {
        List<Book> results = new ArrayList<>();

        // 模拟搜索结果
        if (query.toLowerCase().contains("java")) {
            Book book1 = new Book("Java编程思想", "Bruce Eckel", "9787111213826");
            book1.setBookId(1);
            book1.setDescription("Java编程经典教材");
            book1.setAvailableCopies(3);
            results.add(book1);

            Book book2 = new Book("Effective Java", "Joshua Bloch", "9787111255833");
            book2.setBookId(2);
            book2.setDescription("Java最佳实践指南");
            book2.setAvailableCopies(2);
            results.add(book2);
        }

        if (query.toLowerCase().contains("android")) {
            Book book3 = new Book("Android开发艺术探索", "任玉刚", "9787121269394");
            book3.setBookId(3);
            book3.setDescription("Android进阶开发指南");
            book3.setAvailableCopies(1);
            results.add(book3);
        }

        if (query.toLowerCase().contains("红楼梦") || query.toLowerCase().contains("文学")) {
            Book book4 = new Book("红楼梦", "曹雪芹", "9787020002207");
            book4.setBookId(4);
            book4.setDescription("中国古典文学四大名著之一");
            book4.setAvailableCopies(5);
            results.add(book4);
        }

        if (query.toLowerCase().contains("spring") || query.toLowerCase().contains("boot")) {
            Book book5 = new Book("Spring Boot实战", "Craig Walls", "9787115416179");
            book5.setBookId(7);
            book5.setDescription("Spring Boot开发实战指南");
            book5.setAvailableCopies(3);
            results.add(book5);
        }

        if (query.toLowerCase().contains("三体") || query.toLowerCase().contains("科幻")) {
            Book book6 = new Book("三体", "刘慈欣", "9787536692930");
            book6.setBookId(10);
            book6.setDescription("中国科幻文学的里程碑之作");
            book6.setAvailableCopies(6);
            results.add(book6);
        }

        if (query.toLowerCase().contains("设计模式") || query.toLowerCase().contains("pattern")) {
            Book book7 = new Book("设计模式", "Gang of Four", "9787111075776");
            book7.setBookId(9);
            book7.setDescription("软件设计模式经典教材");
            book7.setAvailableCopies(4);
            results.add(book7);
        }

        return results;
    }

    private void showSearchHint() {
        llSearchHint.setVisibility(View.VISIBLE);
        rvSearchResults.setVisibility(View.GONE);
        llEmptyView.setVisibility(View.GONE);
    }

    private void showSearchResults() {
        llSearchHint.setVisibility(View.GONE);
        rvSearchResults.setVisibility(View.VISIBLE);
        llEmptyView.setVisibility(View.GONE);
    }

    private void showEmptyView() {
        llSearchHint.setVisibility(View.GONE);
        rvSearchResults.setVisibility(View.GONE);
        llEmptyView.setVisibility(View.VISIBLE);
    }

    private void hideAllViews() {
        llSearchHint.setVisibility(View.GONE);
        rvSearchResults.setVisibility(View.GONE);
        llEmptyView.setVisibility(View.GONE);
    }
}
