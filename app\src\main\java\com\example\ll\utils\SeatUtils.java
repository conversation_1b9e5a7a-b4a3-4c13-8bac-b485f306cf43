package com.example.ll.utils;

import com.example.ll.model.Seat;
import com.example.ll.model.SeatReservation;

import java.util.ArrayList;
import java.util.List;

public class SeatUtils {
    
    // 座位状态常量
    public static final String STATUS_AVAILABLE = "available";
    public static final String STATUS_OCCUPIED = "occupied";
    public static final String STATUS_MAINTENANCE = "maintenance";
    public static final String STATUS_RESERVED = "reserved";
    
    // 预约状态常量
    public static final String RESERVATION_ACTIVE = "active";
    public static final String RESERVATION_COMPLETED = "completed";
    public static final String RESERVATION_CANCELLED = "cancelled";
    public static final String RESERVATION_NO_SHOW = "no_show";
    
    // 座位类型常量
    public static final String TYPE_SINGLE = "single";
    public static final String TYPE_DOUBLE = "double";
    public static final String TYPE_COMPUTER = "computer";
    public static final String TYPE_GROUP = "group";
    
    /**
     * 检查座位是否可用
     */
    public static boolean isSeatAvailable(Seat seat) {
        return seat != null && STATUS_AVAILABLE.equals(seat.getStatus());
    }
    
    /**
     * 检查座位是否被占用
     */
    public static boolean isSeatOccupied(Seat seat) {
        return seat != null && STATUS_OCCUPIED.equals(seat.getStatus());
    }
    
    /**
     * 检查座位是否在维护中
     */
    public static boolean isSeatMaintenance(Seat seat) {
        return seat != null && STATUS_MAINTENANCE.equals(seat.getStatus());
    }
    
    /**
     * 检查预约是否有效
     */
    public static boolean isReservationActive(SeatReservation reservation) {
        return reservation != null && RESERVATION_ACTIVE.equals(reservation.getStatus());
    }
    
    /**
     * 检查预约是否已完成
     */
    public static boolean isReservationCompleted(SeatReservation reservation) {
        return reservation != null && RESERVATION_COMPLETED.equals(reservation.getStatus());
    }
    
    /**
     * 检查预约是否已取消
     */
    public static boolean isReservationCancelled(SeatReservation reservation) {
        return reservation != null && RESERVATION_CANCELLED.equals(reservation.getStatus());
    }
    
    /**
     * 检查是否可以签到
     */
    public static boolean canCheckIn(SeatReservation reservation) {
        if (reservation == null || !isReservationActive(reservation)) {
            return false;
        }
        
        // 检查是否已经签到
        if (reservation.getCheckInTime() > 0) {
            return false;
        }
        
        // 检查是否在预约时间范围内
        String currentTime = DateUtils.getCurrentTime();
        String currentDate = DateUtils.getCurrentDate();
        
        // 只能在预约当天签到
        if (!currentDate.equals(reservation.getReservationDate())) {
            return false;
        }
        
        // 可以在预约开始时间前15分钟签到
        String allowedStartTime = subtractMinutes(reservation.getStartTime(), 15);
        
        return DateUtils.isTimeInRange(currentTime, allowedStartTime, reservation.getEndTime());
    }
    
    /**
     * 检查是否可以签退
     */
    public static boolean canCheckOut(SeatReservation reservation) {
        if (reservation == null || !isReservationActive(reservation)) {
            return false;
        }
        
        // 必须已经签到
        if (reservation.getCheckInTime() == 0) {
            return false;
        }
        
        // 还没有签退
        return reservation.getCheckOutTime() == 0;
    }
    
    /**
     * 检查是否可以取消预约
     */
    public static boolean canCancelReservation(SeatReservation reservation) {
        if (reservation == null || !isReservationActive(reservation)) {
            return false;
        }
        
        // 已经签到的预约不能取消
        if (reservation.getCheckInTime() > 0) {
            return false;
        }
        
        // 只能在预约开始前取消
        String currentTime = DateUtils.getCurrentTime();
        String currentDate = DateUtils.getCurrentDate();
        
        if (currentDate.equals(reservation.getReservationDate())) {
            return DateUtils.compareTime(currentTime, reservation.getStartTime()) < 0;
        } else {
            // 未来日期的预约可以取消
            return DateUtils.compareTime(currentDate, reservation.getReservationDate()) < 0;
        }
    }
    
    /**
     * 检查时间段是否冲突
     */
    public static boolean hasTimeConflict(String startTime1, String endTime1, 
                                         String startTime2, String endTime2) {
        return DateUtils.isTimeRangeOverlap(startTime1, endTime1, startTime2, endTime2);
    }
    
    /**
     * 检查座位在指定时间段是否可预约
     */
    public static boolean isSeatAvailableForReservation(Seat seat, String date,
                                                       String startTime, String endTime,
                                                       List<SeatReservation> existingReservations) {
        // 座位本身必须可用
        if (!isSeatAvailable(seat)) {
            return false;
        }

        // 检查是否与现有预约冲突
        for (SeatReservation reservation : existingReservations) {
            if (reservation.getSeatId() == seat.getSeatId() &&
                date.equals(reservation.getReservationDate()) &&
                isReservationActive(reservation)) {

                if (hasTimeConflict(startTime, endTime,
                                  reservation.getStartTime(), reservation.getEndTime())) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 从预约列表中过滤出与指定时间段冲突的预约
     */
    public static List<SeatReservation> filterConflictingReservations(
            List<SeatReservation> reservations, String startTime, String endTime) {
        List<SeatReservation> conflicts = new ArrayList<>();
        for (SeatReservation reservation : reservations) {
            if (isReservationActive(reservation) &&
                hasTimeConflict(startTime, endTime,
                               reservation.getStartTime(), reservation.getEndTime())) {
                conflicts.add(reservation);
            }
        }
        return conflicts;
    }
    
    /**
     * 获取座位类型的显示文本
     */
    public static String getSeatTypeText(String seatType) {
        switch (seatType) {
            case TYPE_SINGLE:
                return "单人座";
            case TYPE_DOUBLE:
                return "双人座";
            case TYPE_COMPUTER:
                return "电脑座";
            case TYPE_GROUP:
                return "小组座";
            default:
                return "普通座";
        }
    }
    
    /**
     * 获取座位状态的显示文本
     */
    public static String getSeatStatusText(String status) {
        switch (status) {
            case STATUS_AVAILABLE:
                return "可用";
            case STATUS_OCCUPIED:
                return "占用";
            case STATUS_MAINTENANCE:
                return "维护";
            case STATUS_RESERVED:
                return "已预约";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取预约状态的显示文本
     */
    public static String getReservationStatusText(String status) {
        switch (status) {
            case RESERVATION_ACTIVE:
                return "有效";
            case RESERVATION_COMPLETED:
                return "已完成";
            case RESERVATION_CANCELLED:
                return "已取消";
            case RESERVATION_NO_SHOW:
                return "未到";
            default:
                return "未知";
        }
    }
    
    /**
     * 生成座位编号
     */
    public static String generateSeatNumber(int roomId, int seatIndex) {
        char roomLetter = (char) ('A' + (roomId - 1) % 26);
        return String.format("%c%03d", roomLetter, seatIndex);
    }
    
    /**
     * 时间减去指定分钟数
     */
    private static String subtractMinutes(String time, int minutes) {
        try {
            String[] parts = time.split(":");
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            
            minute -= minutes;
            if (minute < 0) {
                hour--;
                minute += 60;
            }
            if (hour < 0) {
                hour = 0;
                minute = 0;
            }
            
            return String.format("%02d:%02d", hour, minute);
        } catch (Exception e) {
            return time;
        }
    }
    
    /**
     * 时间加上指定分钟数
     */
    public static String addMinutes(String time, int minutes) {
        try {
            String[] parts = time.split(":");
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            
            minute += minutes;
            if (minute >= 60) {
                hour++;
                minute -= 60;
            }
            if (hour >= 24) {
                hour = 23;
                minute = 59;
            }
            
            return String.format("%02d:%02d", hour, minute);
        } catch (Exception e) {
            return time;
        }
    }
    
    /**
     * 获取预约时长（分钟）
     */
    public static int getReservationDuration(SeatReservation reservation) {
        return DateUtils.getMinutesBetween(reservation.getStartTime(), reservation.getEndTime());
    }
    
    /**
     * 检查预约时长是否合理（最少30分钟，最多8小时）
     */
    public static boolean isValidReservationDuration(String startTime, String endTime) {
        int duration = DateUtils.getMinutesBetween(startTime, endTime);
        return duration >= 30 && duration <= 480; // 30分钟到8小时
    }
}
