package com.example.ll.activity;

import androidx.appcompat.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.ll.R;
import com.example.ll.adapter.UserManagementAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.User;
import com.example.ll.utils.PermissionManager;
import com.example.ll.utils.SharedPreferencesManager;
import com.example.ll.utils.UserUtils;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;

public class UserManagementActivity extends AppCompatActivity implements UserManagementAdapter.OnUserActionListener {

    private Toolbar toolbar;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RecyclerView recyclerView;
    private FloatingActionButton fabAddUser;
    
    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private PermissionManager permissionManager;
    private UserManagementAdapter adapter;
    
    private List<User> userList = new ArrayList<>();
    private List<User> filteredUserList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_user_management);
        
        initViews();
        initData();
        setListeners();
        checkPermissions();
        loadUsers();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
        recyclerView = findViewById(R.id.recycler_view);
        fabAddUser = findViewById(R.id.fab_add_user);
        
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("用户管理");
        }
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new UserManagementAdapter(this, filteredUserList, this);
        recyclerView.setAdapter(adapter);
    }
    
    private void initData() {
        database = LibraryDatabase.getInstance(this);
        prefsManager = SharedPreferencesManager.getInstance(this);
        permissionManager = PermissionManager.getInstance(this);
    }
    
    private void setListeners() {
        toolbar.setNavigationOnClickListener(v -> finish());
        
        swipeRefreshLayout.setOnRefreshListener(this::loadUsers);
        
        fabAddUser.setOnClickListener(v -> {
            Intent intent = new Intent(this, RegisterActivity.class);
            intent.putExtra("is_admin_create", true);
            startActivity(intent);
        });
    }
    
    private void checkPermissions() {
        if (!permissionManager.canManageUsers()) {
            Toast.makeText(this, "您没有用户管理权限", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
    }
    
    private void loadUsers() {
        swipeRefreshLayout.setRefreshing(true);
        
        new Thread(() -> {
            try {
                List<User> users = database.userDao().getAllUsers();
                
                runOnUiThread(() -> {
                    userList.clear();
                    userList.addAll(users);
                    
                    filteredUserList.clear();
                    filteredUserList.addAll(users);
                    
                    adapter.notifyDataSetChanged();
                    swipeRefreshLayout.setRefreshing(false);
                });
                
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载用户列表失败", Toast.LENGTH_SHORT).show();
                    swipeRefreshLayout.setRefreshing(false);
                });
            }
        }).start();
    }
    
    private void filterUsers(String query) {
        filteredUserList.clear();
        
        if (TextUtils.isEmpty(query)) {
            filteredUserList.addAll(userList);
        } else {
            String lowerQuery = query.toLowerCase();
            for (User user : userList) {
                if (user.getUsername().toLowerCase().contains(lowerQuery) ||
                    (user.getNickname() != null && user.getNickname().toLowerCase().contains(lowerQuery)) ||
                    (user.getRealName() != null && user.getRealName().toLowerCase().contains(lowerQuery)) ||
                    (user.getEmail() != null && user.getEmail().toLowerCase().contains(lowerQuery))) {
                    filteredUserList.add(user);
                }
            }
        }
        
        adapter.notifyDataSetChanged();
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_user_management, menu);
        
        MenuItem searchItem = menu.findItem(R.id.action_search);
        SearchView searchView = (SearchView) searchItem.getActionView();
        searchView.setQueryHint("搜索用户...");
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                filterUsers(query);
                return true;
            }
            
            @Override
            public boolean onQueryTextChange(String newText) {
                filterUsers(newText);
                return true;
            }
        });
        
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        
        if (itemId == R.id.action_refresh) {
            loadUsers();
            return true;
        } else if (itemId == R.id.action_export) {
            if (permissionManager.canExportData()) {
                exportUserData();
            } else {
                Toast.makeText(this, "您没有数据导出权限", Toast.LENGTH_SHORT).show();
            }
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    private void exportUserData() {
        // TODO: 实现用户数据导出功能
        Toast.makeText(this, "数据导出功能开发中", Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onUserClick(User user) {
        if (permissionManager.canEditUser(user)) {
            Intent intent = new Intent(this, UserDetailActivity.class);
            intent.putExtra("user_id", user.getUserId());
            startActivity(intent);
        } else {
            Toast.makeText(this, "您没有编辑此用户的权限", Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onUserEdit(User user) {
        if (permissionManager.canEditUser(user)) {
            Intent intent = new Intent(this, UserEditActivity.class);
            intent.putExtra("user_id", user.getUserId());
            startActivity(intent);
        } else {
            Toast.makeText(this, "您没有编辑此用户的权限", Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onUserDelete(User user) {
        if (!permissionManager.canDeleteUser(user)) {
            Toast.makeText(this, "您没有删除此用户的权限", Toast.LENGTH_SHORT).show();
            return;
        }
        
        new AlertDialog.Builder(this)
            .setTitle("删除用户")
            .setMessage("确定要删除用户 " + UserUtils.getDisplayName(user) + " 吗？\n此操作不可恢复！")
            .setPositiveButton("删除", (dialog, which) -> deleteUser(user))
            .setNegativeButton("取消", null)
            .show();
    }
    
    @Override
    public void onUserFreeze(User user) {
        if (!permissionManager.canFreezeUser(user)) {
            Toast.makeText(this, "您没有冻结此用户的权限", Toast.LENGTH_SHORT).show();
            return;
        }
        
        boolean isCurrentlyFrozen = UserUtils.isUserFrozen(user);
        String action = isCurrentlyFrozen ? "解冻" : "冻结";
        
        new AlertDialog.Builder(this)
            .setTitle(action + "用户")
            .setMessage("确定要" + action + "用户 " + UserUtils.getDisplayName(user) + " 吗？")
            .setPositiveButton(action, (dialog, which) -> toggleUserFreeze(user))
            .setNegativeButton("取消", null)
            .show();
    }
    
    private void deleteUser(User user) {
        new Thread(() -> {
            try {
                user.setStatus(UserUtils.STATUS_DELETED);
                user.setUpdatedAt(System.currentTimeMillis());
                database.userDao().updateUser(user);
                
                runOnUiThread(() -> {
                    Toast.makeText(this, "用户删除成功", Toast.LENGTH_SHORT).show();
                    loadUsers();
                });
                
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "删除用户失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }
    
    private void toggleUserFreeze(User user) {
        new Thread(() -> {
            try {
                boolean isCurrentlyFrozen = UserUtils.isUserFrozen(user);
                String newStatus = isCurrentlyFrozen ? UserUtils.STATUS_ACTIVE : UserUtils.STATUS_FROZEN;
                
                user.setStatus(newStatus);
                user.setUpdatedAt(System.currentTimeMillis());
                database.userDao().updateUser(user);
                
                runOnUiThread(() -> {
                    String action = isCurrentlyFrozen ? "解冻" : "冻结";
                    Toast.makeText(this, "用户" + action + "成功", Toast.LENGTH_SHORT).show();
                    loadUsers();
                });
                
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "操作失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        loadUsers(); // 从其他页面返回时刷新数据
    }
}
