package com.example.ll.model;

import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "book_categories")
public class BookCategory {
    @PrimaryKey(autoGenerate = true)
    private int categoryId;
    private String categoryName;
    private int parentId;
    private String description;
    private int sortOrder;
    private long createdAt;

    // 构造函数
    public BookCategory() {
        this.createdAt = System.currentTimeMillis();
        this.sortOrder = 0;
    }

    @Ignore
    public BookCategory(String categoryName, String description) {
        this();
        this.categoryName = categoryName;
        this.description = description;
    }

    // Getter和Setter方法
    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return categoryName;
    }
}
