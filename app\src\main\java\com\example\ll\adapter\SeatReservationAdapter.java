package com.example.ll.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.example.ll.R;
import com.example.ll.model.SeatReservation;
import com.example.ll.utils.DateUtils;

import java.util.List;

public class SeatReservationAdapter extends RecyclerView.Adapter<SeatReservationAdapter.ReservationViewHolder> {

    private Context context;
    private List<SeatReservation> reservations;
    private OnReservationActionListener listener;

    public interface OnReservationActionListener {
        void onCheckIn(SeatReservation reservation);
        void onCheckOut(SeatReservation reservation);
        void onCancel(SeatReservation reservation);
    }

    public SeatReservationAdapter(Context context, List<SeatReservation> reservations) {
        this.context = context;
        this.reservations = reservations;
    }

    public void setOnReservationActionListener(OnReservationActionListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ReservationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_seat_reservation, parent, false);
        return new ReservationViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ReservationViewHolder holder, int position) {
        SeatReservation reservation = reservations.get(position);
        holder.bind(reservation);
    }

    @Override
    public int getItemCount() {
        return reservations.size();
    }

    public class ReservationViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private TextView tvRoomName, tvSeatNumber, tvDate, tvTime, tvStatus;
        private Button btnCheckIn, btnCheckOut, btnCancel;

        public ReservationViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_reservation);
            tvRoomName = itemView.findViewById(R.id.tv_room_name);
            tvSeatNumber = itemView.findViewById(R.id.tv_seat_number);
            tvDate = itemView.findViewById(R.id.tv_date);
            tvTime = itemView.findViewById(R.id.tv_time);
            tvStatus = itemView.findViewById(R.id.tv_status);
            btnCheckIn = itemView.findViewById(R.id.btn_check_in);
            btnCheckOut = itemView.findViewById(R.id.btn_check_out);
            btnCancel = itemView.findViewById(R.id.btn_cancel);
        }

        public void bind(SeatReservation reservation) {
            if (reservation.getStudyRoom() != null) {
                tvRoomName.setText(reservation.getStudyRoom().getRoomName());
            }
            
            if (reservation.getSeat() != null) {
                tvSeatNumber.setText("座位号：" + reservation.getSeat().getSeatNumber());
            }
            
            tvDate.setText(DateUtils.formatDisplayDate(DateUtils.parseDate(reservation.getReservationDate(), DateUtils.DATE_FORMAT)));
            tvTime.setText(reservation.getStartTime() + " - " + reservation.getEndTime());
            
            // 设置状态
            String status = getStatusText(reservation);
            tvStatus.setText(status);
            setStatusColor(reservation);
            
            // 设置按钮状态
            setupButtons(reservation);
        }

        private String getStatusText(SeatReservation reservation) {
            if (reservation.getCheckInTime() > 0 && reservation.getCheckOutTime() == 0) {
                return "已签到";
            } else if (reservation.getCheckOutTime() > 0) {
                return "已完成";
            } else if (reservation.canCheckIn()) {
                return "待签到";
            } else {
                return "预约中";
            }
        }

        private void setStatusColor(SeatReservation reservation) {
            if (reservation.getCheckOutTime() > 0) {
                tvStatus.setTextColor(context.getResources().getColor(R.color.success_color));
            } else if (reservation.getCheckInTime() > 0) {
                tvStatus.setTextColor(context.getResources().getColor(R.color.primary_color));
            } else {
                tvStatus.setTextColor(context.getResources().getColor(R.color.warning_color));
            }
        }

        private void setupButtons(SeatReservation reservation) {
            // 重置按钮状态
            btnCheckIn.setVisibility(View.GONE);
            btnCheckOut.setVisibility(View.GONE);
            btnCancel.setVisibility(View.VISIBLE);

            if (reservation.canCheckIn()) {
                btnCheckIn.setVisibility(View.VISIBLE);
                btnCheckIn.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onCheckIn(reservation);
                    }
                });
            } else if (reservation.canCheckOut()) {
                btnCheckOut.setVisibility(View.VISIBLE);
                btnCheckOut.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onCheckOut(reservation);
                    }
                });
            }

            if (reservation.getCheckOutTime() > 0) {
                btnCancel.setVisibility(View.GONE);
            } else {
                btnCancel.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onCancel(reservation);
                    }
                });
            }
        }
    }
}
