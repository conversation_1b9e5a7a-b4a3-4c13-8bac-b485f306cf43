package com.example.ll.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.example.ll.R;
import com.example.ll.model.BookCategory;

import java.util.List;

public class BookCategoryAdapter extends RecyclerView.Adapter<BookCategoryAdapter.CategoryViewHolder> {

    private Context context;
    private List<BookCategory> categories;
    private int selectedPosition = -1; // -1表示选中"全部"
    private OnCategorySelectedListener listener;

    public interface OnCategorySelectedListener {
        void onCategorySelected(BookCategory category);
        void onAllCategoriesSelected();
    }

    public BookCategoryAdapter(Context context, List<BookCategory> categories) {
        this.context = context;
        this.categories = categories;
    }

    public void setOnCategorySelectedListener(OnCategorySelectedListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_book_category, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        if (position == 0) {
            // 第一个位置显示"全部"
            holder.bind(null, position);
        } else {
            BookCategory category = categories.get(position - 1);
            holder.bind(category, position);
        }
    }

    @Override
    public int getItemCount() {
        return categories.size() + 1; // +1 for "全部"
    }

    public void updateCategories(List<BookCategory> newCategories) {
        this.categories.clear();
        this.categories.addAll(newCategories);
        notifyDataSetChanged();
    }

    public class CategoryViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private TextView tvCategoryName;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_category);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
        }

        public void bind(BookCategory category, int position) {
            if (category == null) {
                // "全部" 选项
                tvCategoryName.setText("全部");
            } else {
                tvCategoryName.setText(category.getCategoryName());
            }

            // 设置选中状态
            boolean isSelected = (selectedPosition == position);
            if (isSelected) {
                cardView.setCardBackgroundColor(context.getResources().getColor(R.color.primary_color));
                tvCategoryName.setTextColor(context.getResources().getColor(R.color.text_white));
            } else {
                cardView.setCardBackgroundColor(context.getResources().getColor(R.color.surface_color));
                tvCategoryName.setTextColor(context.getResources().getColor(R.color.text_primary));
            }

            // 点击事件
            cardView.setOnClickListener(v -> {
                int oldPosition = selectedPosition;
                selectedPosition = position;
                
                // 刷新旧的和新的选中项
                if (oldPosition != -1) {
                    notifyItemChanged(oldPosition);
                }
                notifyItemChanged(selectedPosition);

                // 回调
                if (listener != null) {
                    if (category == null) {
                        listener.onAllCategoriesSelected();
                    } else {
                        listener.onCategorySelected(category);
                    }
                }
            });
        }
    }
}
