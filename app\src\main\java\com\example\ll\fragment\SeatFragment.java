package com.example.ll.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.ll.R;
import com.example.ll.activity.SeatReservationActivity;
import com.example.ll.adapter.SeatReservationAdapter;
import com.example.ll.adapter.StudyRoomAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.SeatReservation;
import com.example.ll.model.StudyRoom;
import com.example.ll.utils.SharedPreferencesManager;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;

public class SeatFragment extends Fragment {

    private TextView tvActiveCount, tvTodayCount, tvTotalCount;
    private RecyclerView rvStudyRooms, rvMyReservations;
    private SwipeRefreshLayout swipeRefresh;
    private FloatingActionButton fabNewReservation;
    private LinearLayout llEmptyView;
    
    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private StudyRoomAdapter roomAdapter;
    private SeatReservationAdapter reservationAdapter;
    
    private List<SeatReservation> myReservations = new ArrayList<>();

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_seat, container, false);
        
        initViews(view);
        initData();
        setListeners();
        loadData();
        
        return view;
    }

    private void initViews(View view) {
        tvActiveCount = view.findViewById(R.id.tv_active_count);
        tvTodayCount = view.findViewById(R.id.tv_today_count);
        tvTotalCount = view.findViewById(R.id.tv_total_count);
        
        rvStudyRooms = view.findViewById(R.id.rv_study_rooms);
        rvMyReservations = view.findViewById(R.id.rv_my_reservations);
        swipeRefresh = view.findViewById(R.id.swipe_refresh);
        fabNewReservation = view.findViewById(R.id.fab_new_reservation);
        llEmptyView = view.findViewById(R.id.tv_empty_view);
        
        // 设置布局管理器
        rvStudyRooms.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        rvMyReservations.setLayoutManager(new LinearLayoutManager(getContext()));
    }

    private void initData() {
        database = LibraryDatabase.getInstance(getContext());
        prefsManager = SharedPreferencesManager.getInstance(getContext());
        
        roomAdapter = new StudyRoomAdapter(getContext(), new ArrayList<>());
        rvStudyRooms.setAdapter(roomAdapter);
        
        reservationAdapter = new SeatReservationAdapter(getContext(), myReservations);
        rvMyReservations.setAdapter(reservationAdapter);
        
        // 设置自习室点击监听
        roomAdapter.setOnRoomClickListener(room -> {
            Intent intent = new Intent(getContext(), SeatReservationActivity.class);
            intent.putExtra("room_id", room.getRoomId());
            startActivity(intent);
        });
        
        // 设置预约操作监听
        reservationAdapter.setOnReservationActionListener(new SeatReservationAdapter.OnReservationActionListener() {
            @Override
            public void onCheckIn(SeatReservation reservation) {
                checkInReservation(reservation);
            }
            
            @Override
            public void onCheckOut(SeatReservation reservation) {
                checkOutReservation(reservation);
            }
            
            @Override
            public void onCancel(SeatReservation reservation) {
                cancelReservation(reservation);
            }
        });
    }

    private void setListeners() {
        swipeRefresh.setOnRefreshListener(this::loadData);
        
        fabNewReservation.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), SeatReservationActivity.class);
            startActivity(intent);
        });
    }

    private void loadData() {
        swipeRefresh.setRefreshing(true);
        
        new Thread(() -> {
            try {
                int userId = prefsManager.getCurrentUserId();
                
                // 获取自习室列表
                List<StudyRoom> studyRooms = database.studyRoomDao().getAllAvailableRooms();
                
                // 获取我的预约记录
                List<SeatReservation> activeReservations = database.seatReservationDao().getActiveReservationsByUser(userId);
                List<SeatReservation> todayReservations = database.seatReservationDao().getUserReservationsForDate(userId, getCurrentDate());
                List<SeatReservation> allReservations = database.seatReservationDao().getReservationsByUser(userId);
                
                // 为预约记录加载座位和房间信息
                for (SeatReservation reservation : activeReservations) {
                    loadReservationDetails(reservation);
                }
                
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        // 更新统计信息
                        tvActiveCount.setText(String.valueOf(activeReservations.size()));
                        tvTodayCount.setText(String.valueOf(todayReservations.size()));
                        tvTotalCount.setText(String.valueOf(allReservations.size()));
                        
                        // 更新自习室列表
                        roomAdapter.updateRooms(studyRooms);
                        
                        // 更新我的预约列表
                        myReservations.clear();
                        myReservations.addAll(activeReservations);
                        reservationAdapter.notifyDataSetChanged();
                        
                        // 显示/隐藏空视图
                        if (myReservations.isEmpty()) {
                            rvMyReservations.setVisibility(View.GONE);
                            llEmptyView.setVisibility(View.VISIBLE);
                        } else {
                            rvMyReservations.setVisibility(View.VISIBLE);
                            llEmptyView.setVisibility(View.GONE);
                        }
                        
                        swipeRefresh.setRefreshing(false);
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        swipeRefresh.setRefreshing(false);
                    });
                }
            }
        }).start();
    }

    private void loadReservationDetails(SeatReservation reservation) {
        try {
            // 加载座位信息
            reservation.setSeat(database.seatDao().getSeatById(reservation.getSeatId()));
            
            // 加载房间信息
            if (reservation.getSeat() != null) {
                reservation.setStudyRoom(database.studyRoomDao().getRoomById(reservation.getSeat().getRoomId()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void checkInReservation(SeatReservation reservation) {
        new Thread(() -> {
            try {
                database.seatReservationDao().checkIn(reservation.getReservationId(), System.currentTimeMillis());
                
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        loadData(); // 刷新数据
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    private void checkOutReservation(SeatReservation reservation) {
        new Thread(() -> {
            try {
                database.seatReservationDao().checkOut(reservation.getReservationId(), System.currentTimeMillis());
                database.seatReservationDao().updateReservationStatus(reservation.getReservationId(), "completed");
                
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        loadData(); // 刷新数据
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    private void cancelReservation(SeatReservation reservation) {
        new Thread(() -> {
            try {
                database.seatReservationDao().updateReservationStatus(reservation.getReservationId(), "cancelled");
                
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        loadData(); // 刷新数据
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    private String getCurrentDate() {
        return com.example.ll.utils.DateUtils.getCurrentDate();
    }

    @Override
    public void onResume() {
        super.onResume();
        loadData(); // 页面恢复时刷新数据
    }
}
