<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 顶部标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary_color"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="座位预约"
                android:textColor="@android:color/white"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="选择您的学习空间"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:alpha="0.9" />

        </LinearLayout>

        <!-- 统计卡片区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-40dp"
            android:layout_marginHorizontal="16dp"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_active_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="活跃预约"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_today_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/warning_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="今日预约"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_total_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="总预约"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- 内容区域 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="24dp">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 自习室列表 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_margin="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="选择自习室"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="16dp" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_study_rooms"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <!-- 我的预约 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_margin="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="我的预约"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="16dp" />

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_my_reservations"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />

                            <!-- 空状态视图 -->
                            <LinearLayout
                                android:id="@+id/tv_empty_view"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="32dp"
                                android:visibility="gone">

                                <ImageView
                                    android:layout_width="64dp"
                                    android:layout_height="64dp"
                                    android:src="@drawable/ic_empty_seat"
                                    android:tint="@color/text_hint"
                                    android:layout_marginBottom="16dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="暂无预约记录"
                                    android:textSize="16sp"
                                    android:textColor="@color/text_secondary" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="点击右下角按钮开始预约座位"
                                    android:textSize="14sp"
                                    android:textColor="@color/text_hint"
                                    android:layout_marginTop="8dp" />

                            </LinearLayout>

                        </FrameLayout>

                    </LinearLayout>

                </LinearLayout>

            </ScrollView>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

    <!-- 浮动按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_new_reservation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:tint="@android:color/white"
        app:backgroundTint="@color/primary_color" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
