package com.example.ll.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.ll.model.User;

import java.util.List;

@Dao
public interface UserDao {
    
    @Insert
    long insertUser(User user);
    
    @Update
    void updateUser(User user);
    
    @Delete
    void deleteUser(User user);
    
    @Query("SELECT * FROM users WHERE userId = :userId")
    User getUserById(int userId);
    
    @Query("SELECT * FROM users WHERE username = :username")
    User getUserByUsername(String username);
    
    @Query("SELECT * FROM users WHERE email = :email")
    User getUserByEmail(String email);
    
    @Query("SELECT * FROM users WHERE phone = :phone")
    User getUserByPhone(String phone);
    
    @Query("SELECT * FROM users WHERE username = :username AND password = :password")
    User login(String username, String password);
    
    @Query("SELECT * FROM users WHERE (username = :account OR email = :account OR phone = :account) AND password = :password")
    User loginWithAccount(String account, String password);
    
    @Query("SELECT * FROM users WHERE status = 'active' ORDER BY createdAt DESC")
    List<User> getAllActiveUsers();
    
    @Query("SELECT * FROM users ORDER BY createdAt DESC")
    List<User> getAllUsers();
    
    @Query("SELECT * FROM users WHERE (username LIKE :keyword OR nickname LIKE :keyword OR realName LIKE :keyword) AND status = 'active'")
    List<User> searchUsers(String keyword);
    
    @Query("UPDATE users SET status = :status WHERE userId = :userId")
    void updateUserStatus(int userId, String status);
    
    @Query("UPDATE users SET password = :newPassword WHERE userId = :userId")
    void updatePassword(int userId, String newPassword);
    
    @Query("SELECT COUNT(*) FROM users WHERE status = 'active'")
    int getActiveUserCount();
    
    @Query("SELECT * FROM users WHERE status = 'frozen'")
    List<User> getFrozenUsers();
}
