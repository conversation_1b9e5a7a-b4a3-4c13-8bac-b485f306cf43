package com.example.ll.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.ll.R;
import com.example.ll.adapter.BorrowingRecordAdapter;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Book;
import com.example.ll.model.BorrowingRecord;
import com.example.ll.utils.SharedPreferencesManager;

import java.util.ArrayList;
import java.util.List;

public class BorrowingFragment extends Fragment {

    private TextView tvCurrentCount, tvOverdueCount, tvTotalCount;
    private RecyclerView rvBorrowingRecords;
    private SwipeRefreshLayout swipeRefresh;
    private LinearLayout llEmptyView;
    
    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private BorrowingRecordAdapter recordAdapter;
    
    private List<BorrowingRecord> borrowingRecords = new ArrayList<>();

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_borrowing, container, false);
        
        initViews(view);
        initData();
        setListeners();
        loadData();
        
        return view;
    }

    private void initViews(View view) {
        tvCurrentCount = view.findViewById(R.id.tv_current_count);
        tvOverdueCount = view.findViewById(R.id.tv_overdue_count);
        tvTotalCount = view.findViewById(R.id.tv_total_count);
        
        rvBorrowingRecords = view.findViewById(R.id.rv_borrowing_records);
        swipeRefresh = view.findViewById(R.id.swipe_refresh);
        llEmptyView = view.findViewById(R.id.tv_empty_view);
        
        rvBorrowingRecords.setLayoutManager(new LinearLayoutManager(getContext()));
    }

    private void initData() {
        database = LibraryDatabase.getInstance(getContext());
        prefsManager = SharedPreferencesManager.getInstance(getContext());
        
        recordAdapter = new BorrowingRecordAdapter(getContext(), borrowingRecords);
        rvBorrowingRecords.setAdapter(recordAdapter);
        
        // 设置续借和归还监听
        recordAdapter.setOnRecordActionListener(new BorrowingRecordAdapter.OnRecordActionListener() {
            @Override
            public void onRenewBook(BorrowingRecord record) {
                renewBook(record);
            }
            
            @Override
            public void onReturnBook(BorrowingRecord record) {
                returnBook(record);
            }
        });
    }

    private void setListeners() {
        swipeRefresh.setOnRefreshListener(this::loadData);
    }

    private void loadData() {
        swipeRefresh.setRefreshing(true);
        
        new Thread(() -> {
            try {
                int userId = prefsManager.getCurrentUserId();
                
                // 获取当前借阅记录
                List<BorrowingRecord> currentRecords = database.borrowingRecordDao().getCurrentBorrowingsByUser(userId);
                
                // 获取逾期记录
                List<BorrowingRecord> overdueRecords = database.borrowingRecordDao().getUserOverdueRecords(userId, System.currentTimeMillis());
                
                // 获取所有借阅记录
                List<BorrowingRecord> allRecords = database.borrowingRecordDao().getBorrowingRecordsByUser(userId);
                
                // 为每个记录加载图书信息
                for (BorrowingRecord record : currentRecords) {
                    Book book = database.bookDao().getBookById(record.getBookId());
                    record.setBook(book);
                }
                
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        // 更新统计信息
                        tvCurrentCount.setText(String.valueOf(currentRecords.size()));
                        tvOverdueCount.setText(String.valueOf(overdueRecords.size()));
                        tvTotalCount.setText(String.valueOf(allRecords.size()));
                        
                        // 更新列表
                        borrowingRecords.clear();
                        borrowingRecords.addAll(currentRecords);
                        recordAdapter.notifyDataSetChanged();
                        
                        // 显示/隐藏空视图
                        if (borrowingRecords.isEmpty()) {
                            rvBorrowingRecords.setVisibility(View.GONE);
                            llEmptyView.setVisibility(View.VISIBLE);
                        } else {
                            rvBorrowingRecords.setVisibility(View.VISIBLE);
                            llEmptyView.setVisibility(View.GONE);
                        }
                        
                        swipeRefresh.setRefreshing(false);
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        swipeRefresh.setRefreshing(false);
                    });
                }
            }
        }).start();
    }

    private void renewBook(BorrowingRecord record) {
        new Thread(() -> {
            try {
                // 计算新的到期时间（延长30天）
                long newDueDate = record.getDueDate() + (30L * 24 * 60 * 60 * 1000);
                
                database.borrowingRecordDao().renewBook(record.getRecordId(), newDueDate);
                
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        loadData(); // 刷新数据
                        // 可以显示续借成功的提示
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    private void returnBook(BorrowingRecord record) {
        new Thread(() -> {
            try {
                // 归还图书
                database.borrowingRecordDao().returnBook(record.getRecordId(), System.currentTimeMillis());
                
                // 增加图书可借数量
                database.bookDao().returnBook(record.getBookId());
                
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        loadData(); // 刷新数据
                        // 可以显示归还成功的提示
                    });
                }
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    @Override
    public void onResume() {
        super.onResume();
        loadData(); // 页面恢复时刷新数据
    }
}
