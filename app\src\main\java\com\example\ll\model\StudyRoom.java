package com.example.ll.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "study_rooms")
public class StudyRoom {
    @PrimaryKey(autoGenerate = true)
    private int roomId;
    private String roomName;
    private String roomType; // study_room, reading_room, computer_room
    private int capacity;
    private String location;
    private String facilities;
    private String status; // available, maintenance, closed
    private long createdAt;
    private long updatedAt;

    // 构造函数
    public StudyRoom() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.status = "available";
        this.roomType = "study_room";
    }

    public StudyRoom(String roomName, int capacity, String location) {
        this();
        this.roomName = roomName;
        this.capacity = capacity;
        this.location = location;
    }

    // Getter和Setter方法
    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public int getCapacity() {
        return capacity;
    }

    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getFacilities() {
        return facilities;
    }

    public void setFacilities(String facilities) {
        this.facilities = facilities;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 判断房间是否可用
    public boolean isAvailable() {
        return "available".equals(status);
    }

    @Override
    public String toString() {
        return "StudyRoom{" +
                "roomId=" + roomId +
                ", roomName='" + roomName + '\'' +
                ", roomType='" + roomType + '\'' +
                ", capacity=" + capacity +
                ", location='" + location + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
