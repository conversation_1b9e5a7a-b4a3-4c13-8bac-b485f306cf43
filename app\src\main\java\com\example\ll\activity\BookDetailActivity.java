package com.example.ll.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.ll.R;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.Book;
import com.example.ll.model.BorrowingRecord;
import com.example.ll.utils.SharedPreferencesManager;

import java.util.Calendar;

public class BookDetailActivity extends AppCompatActivity {

    public static final String EXTRA_BOOK_ID = "book_id";

    private Toolbar toolbar;
    private ImageView ivBookCover;
    private TextView tvTitle, tvAuthor, tvIsbn, tvPublisher, tvPublishDate,
                     tvCategory, tvDescription, tvLocation, tvAvailableCopies, tvStatus;
    private Button btnBorrow, btnReserve;

    private LibraryDatabase database;
    private SharedPreferencesManager prefsManager;
    private Book currentBook;
    private int bookId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_book_detail);

        initViews();
        initData();
        setListeners();
        loadBookDetail();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("图书详情");

        ivBookCover = findViewById(R.id.iv_book_cover);
        tvTitle = findViewById(R.id.tv_title);
        tvAuthor = findViewById(R.id.tv_author);
        tvIsbn = findViewById(R.id.tv_isbn);
        tvPublisher = findViewById(R.id.tv_publisher);
        tvPublishDate = findViewById(R.id.tv_publish_date);
        tvCategory = findViewById(R.id.tv_category);
        tvDescription = findViewById(R.id.tv_description);
        tvLocation = findViewById(R.id.tv_location);
        tvAvailableCopies = findViewById(R.id.tv_available_copies);
        tvStatus = findViewById(R.id.tv_status);
        btnBorrow = findViewById(R.id.btn_borrow);
        btnReserve = findViewById(R.id.btn_reserve);
    }

    private void initData() {
        database = LibraryDatabase.getInstance(this);
        prefsManager = SharedPreferencesManager.getInstance(this);

        bookId = getIntent().getIntExtra("book_id", -1);
        if (bookId == -1) {
            Toast.makeText(this, "图书信息错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
    }

    private void setListeners() {
        toolbar.setNavigationOnClickListener(v -> finish());

        btnBorrow.setOnClickListener(v -> showBorrowDialog());
        btnReserve.setOnClickListener(v -> showReserveDialog());
    }

    private void loadBookDetail() {
        new Thread(() -> {
            try {
                // 从数据库查询图书详情
                currentBook = database.bookDao().getBookById(bookId);

                // 如果数据库中没有找到，使用模拟数据作为后备
                if (currentBook == null) {
                    currentBook = createMockBook(bookId);
                }

                runOnUiThread(() -> {
                    if (currentBook != null) {
                        displayBookInfo();
                    } else {
                        Toast.makeText(this, "图书不存在", Toast.LENGTH_SHORT).show();
                        finish();
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    // 如果数据库查询失败，使用模拟数据作为后备
                    currentBook = createMockBook(bookId);
                    if (currentBook != null) {
                        displayBookInfo();
                    } else {
                        Toast.makeText(this, "图书不存在", Toast.LENGTH_SHORT).show();
                        finish();
                    }
                });
            }
        }).start();
    }

    private Book createMockBook(int bookId) {
        // 创建模拟图书数据
        Book book = new Book();
        book.setBookId(bookId);

        switch (bookId) {
            case 1:
                book.setTitle("Java编程思想");
                book.setAuthor("Bruce Eckel");
                book.setIsbn("9787111213826");
                book.setPublisher("机械工业出版社");
                book.setPublishDate("2007-06-01");
                book.setCategoryId(1);
                book.setDescription("《Java编程思想》是Java程序设计领域的经典之作，深入浅出地介绍了Java语言的核心概念和编程技巧。本书适合有一定编程基础的读者学习Java语言。");
                book.setLocation("A区-2楼-计算机类-001");
                book.setTotalCopies(5);
                book.setAvailableCopies(3);
                break;
            case 2:
                book.setTitle("Effective Java");
                book.setAuthor("Joshua Bloch");
                book.setIsbn("9787111255833");
                book.setPublisher("机械工业出版社");
                book.setPublishDate("2009-01-01");
                book.setCategoryId(1);
                book.setDescription("《Effective Java》是Java之父James Gosling强烈推荐的Java进阶读物，包含了78条极具实用价值的经验规则。");
                book.setLocation("A区-2楼-计算机类-002");
                book.setTotalCopies(3);
                book.setAvailableCopies(2);
                break;
            case 3:
                book.setTitle("Android开发艺术探索");
                book.setAuthor("任玉刚");
                book.setIsbn("9787121269394");
                book.setPublisher("电子工业出版社");
                book.setPublishDate("2015-09-01");
                book.setCategoryId(1);
                book.setDescription("本书是Android开发进阶类书籍，采用理论、源码和实践相结合的方式来阐述高水准的Android应用开发要点。");
                book.setLocation("A区-2楼-计算机类-003");
                book.setTotalCopies(2);
                book.setAvailableCopies(1);
                break;
            case 4:
                book.setTitle("红楼梦");
                book.setAuthor("曹雪芹");
                book.setIsbn("9787020002207");
                book.setPublisher("人民文学出版社");
                book.setPublishDate("1996-12-01");
                book.setCategoryId(2);
                book.setDescription("《红楼梦》是中国古典四大名著之首，是一部具有世界影响力的人情小说作品，举世公认的中国古典小说巅峰之作。");
                book.setLocation("B区-1楼-文学类-001");
                book.setTotalCopies(8);
                book.setAvailableCopies(5);
                break;
            case 7:
                book.setTitle("Spring Boot实战");
                book.setAuthor("Craig Walls");
                book.setIsbn("9787115416179");
                book.setPublisher("人民邮电出版社");
                book.setPublishDate("2016-09-01");
                book.setCategoryId(1);
                book.setDescription("Spring Boot是Spring框架的一个子项目，旨在简化Spring应用的创建、运行、调试、部署等。本书详细介绍了Spring Boot的核心特性。");
                book.setLocation("A区-2楼-计算机类-004");
                book.setTotalCopies(4);
                book.setAvailableCopies(3);
                break;
            case 8:
                book.setTitle("深入理解Java虚拟机");
                book.setAuthor("周志明");
                book.setIsbn("9787111421900");
                book.setPublisher("机械工业出版社");
                book.setPublishDate("2013-09-01");
                book.setCategoryId(1);
                book.setDescription("本书深入浅出地介绍了Java虚拟机的工作原理，是Java程序员进阶必读的经典著作。");
                book.setLocation("A区-2楼-计算机类-005");
                book.setTotalCopies(3);
                book.setAvailableCopies(2);
                break;
            case 9:
                book.setTitle("设计模式");
                book.setAuthor("Gang of Four");
                book.setIsbn("9787111075776");
                book.setPublisher("机械工业出版社");
                book.setPublishDate("2000-09-01");
                book.setCategoryId(1);
                book.setDescription("本书是设计模式领域的经典之作，介绍了23种常用的设计模式，是软件工程师必备的参考书。");
                book.setLocation("A区-2楼-计算机类-006");
                book.setTotalCopies(5);
                book.setAvailableCopies(4);
                break;
            case 10:
                book.setTitle("三体");
                book.setAuthor("刘慈欣");
                book.setIsbn("9787536692930");
                book.setPublisher("重庆出版社");
                book.setPublishDate("2006-05-01");
                book.setCategoryId(2);
                book.setDescription("《三体》是刘慈欣创作的系列长篇科幻小说，是中国科幻文学的里程碑之作，获得了雨果奖等多项国际大奖。");
                book.setLocation("B区-1楼-文学类-002");
                book.setTotalCopies(8);
                book.setAvailableCopies(6);
                break;
            case 11:
                book.setTitle("人类简史");
                book.setAuthor("尤瓦尔·赫拉利");
                book.setIsbn("9787508647357");
                book.setPublisher("中信出版社");
                book.setPublishDate("2014-11-01");
                book.setCategoryId(3);
                book.setDescription("《人类简史》是以色列新锐历史学家尤瓦尔·赫拉利的代表作品，从十万年前有生命迹象开始到21世纪资本、科技交织的人类发展史。");
                book.setLocation("C区-1楼-历史类-001");
                book.setTotalCopies(4);
                book.setAvailableCopies(3);
                break;
            default:
                return null;
        }

        return book;
    }

    private void displayBookInfo() {
        tvTitle.setText(currentBook.getTitle());
        tvAuthor.setText("作者：" + currentBook.getAuthor());
        tvIsbn.setText("ISBN：" + currentBook.getIsbn());
        tvPublisher.setText("出版社：" + currentBook.getPublisher());
        tvPublishDate.setText("出版日期：" + currentBook.getPublishDate());
        tvCategory.setText("分类：" + getCategoryName(currentBook.getCategoryId()));
        tvDescription.setText(currentBook.getDescription());
        tvLocation.setText("位置：" + currentBook.getLocation());
        tvAvailableCopies.setText("可借数量：" + currentBook.getAvailableCopies() + "/" + currentBook.getTotalCopies());

        // 设置状态和按钮
        if (currentBook.getAvailableCopies() > 0) {
            tvStatus.setText("可借阅");
            tvStatus.setTextColor(getColor(R.color.success_color));
            btnBorrow.setEnabled(true);
            btnReserve.setEnabled(false);
        } else {
            tvStatus.setText("已借完");
            tvStatus.setTextColor(getColor(R.color.error_color));
            btnBorrow.setEnabled(false);
            btnReserve.setEnabled(true);
        }

        // 设置默认封面
        ivBookCover.setImageResource(R.drawable.ic_book_cover);
    }

    private String getCategoryName(int categoryId) {
        switch (categoryId) {
            case 1: return "计算机科学";
            case 2: return "文学";
            case 3: return "历史";
            case 4: return "科学";
            default: return "其他";
        }
    }

    private void showBorrowDialog() {
        new AlertDialog.Builder(this)
                .setTitle("借阅图书")
                .setMessage("确定要借阅《" + currentBook.getTitle() + "》吗？\n\n借阅期限：30天")
                .setPositiveButton("确定", (dialog, which) -> {
                    performBorrow();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void showReserveDialog() {
        new AlertDialog.Builder(this)
                .setTitle("预约图书")
                .setMessage("该图书暂时无库存，是否预约？\n\n预约后图书归还时会优先通知您")
                .setPositiveButton("确定", (dialog, which) -> {
                    performReserve();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void performBorrow() {
        new Thread(() -> {
            try {
                // 检查用户是否已借阅此书
                int userId = prefsManager.getCurrentUserId();
                BorrowingRecord existingRecord = database.borrowingRecordDao().getCurrentBorrowingRecord(userId, bookId);

                if (existingRecord != null) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "您已借阅过此书，请先归还", Toast.LENGTH_SHORT).show();
                    });
                    return;
                }

                // 检查图书是否还有库存
                Book book = database.bookDao().getBookById(bookId);
                if (book == null || book.getAvailableCopies() <= 0) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "图书库存不足", Toast.LENGTH_SHORT).show();
                    });
                    return;
                }

                // 创建借阅记录
                Calendar calendar = Calendar.getInstance();
                long borrowDate = calendar.getTimeInMillis();
                calendar.add(Calendar.DAY_OF_MONTH, 30); // 30天借阅期
                long dueDate = calendar.getTimeInMillis();

                BorrowingRecord borrowingRecord = new BorrowingRecord(userId, bookId, dueDate);
                borrowingRecord.setBorrowDate(borrowDate);
                borrowingRecord.setStatus("borrowed");

                // 插入借阅记录
                long recordId = database.borrowingRecordDao().insertBorrowingRecord(borrowingRecord);

                if (recordId > 0) {
                    // 更新图书可借数量
                    book.setAvailableCopies(book.getAvailableCopies() - 1);
                    database.bookDao().updateBook(book);

                    runOnUiThread(() -> {
                        Toast.makeText(this, "借阅成功！请在30天内归还", Toast.LENGTH_LONG).show();

                        // 更新界面显示
                        currentBook.setAvailableCopies(book.getAvailableCopies());
                        displayBookInfo();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "借阅失败，请重试", Toast.LENGTH_SHORT).show();
                    });
                }

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "借阅失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void performReserve() {
        new Thread(() -> {
            try {
                int userId = prefsManager.getCurrentUserId();

                // 检查用户是否已预约此书
                // 注意：这里需要在数据库中添加预约表，暂时使用简单的逻辑

                // 创建预约记录（可以扩展为单独的预约表）
                Calendar calendar = Calendar.getInstance();
                long reserveDate = calendar.getTimeInMillis();

                // 暂时使用借阅记录表来存储预约信息，状态设为"reserved"
                BorrowingRecord reserveRecord = new BorrowingRecord(userId, bookId, reserveDate);
                reserveRecord.setBorrowDate(reserveDate);
                reserveRecord.setStatus("reserved");

                long recordId = database.borrowingRecordDao().insertBorrowingRecord(reserveRecord);

                runOnUiThread(() -> {
                    if (recordId > 0) {
                        Toast.makeText(this, "预约成功！图书归还时会通知您", Toast.LENGTH_LONG).show();
                    } else {
                        Toast.makeText(this, "预约失败，请重试", Toast.LENGTH_SHORT).show();
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "预约失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }
}
