package com.example.ll.activity;

import android.os.Bundle;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.ll.R;

public class BookDetailActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 获取传递的图书ID
        int bookId = getIntent().getIntExtra("book_id", -1);
        
        Toast.makeText(this, "图书详情页面开发中，图书ID: " + bookId, Toast.LENGTH_SHORT).show();
        
        // 暂时直接返回
        finish();
    }
}
