package com.example.ll.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "borrowing_records")
public class BorrowingRecord {
    @PrimaryKey(autoGenerate = true)
    private int recordId;
    private int userId;
    private int bookId;
    private long borrowDate;
    private long dueDate;
    private long returnDate;
    private int renewalCount;
    private String status; // borrowed, returned, overdue, lost
    private double fineAmount;
    private int adminId;
    private String notes;
    private long createdAt;
    private long updatedAt;

    // 关联的图书信息（不存储在数据库中）
    private transient Book book;
    private transient User user;

    // 构造函数
    public BorrowingRecord() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.borrowDate = System.currentTimeMillis();
        this.status = "borrowed";
        this.renewalCount = 0;
        this.fineAmount = 0.0;
    }

    public BorrowingRecord(int userId, int bookId, long dueDate) {
        this();
        this.userId = userId;
        this.bookId = bookId;
        this.dueDate = dueDate;
    }

    // Getter和Setter方法
    public int getRecordId() {
        return recordId;
    }

    public void setRecordId(int recordId) {
        this.recordId = recordId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getBookId() {
        return bookId;
    }

    public void setBookId(int bookId) {
        this.bookId = bookId;
    }

    public long getBorrowDate() {
        return borrowDate;
    }

    public void setBorrowDate(long borrowDate) {
        this.borrowDate = borrowDate;
    }

    public long getDueDate() {
        return dueDate;
    }

    public void setDueDate(long dueDate) {
        this.dueDate = dueDate;
    }

    public long getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(long returnDate) {
        this.returnDate = returnDate;
    }

    public int getRenewalCount() {
        return renewalCount;
    }

    public void setRenewalCount(int renewalCount) {
        this.renewalCount = renewalCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public double getFineAmount() {
        return fineAmount;
    }

    public void setFineAmount(double fineAmount) {
        this.fineAmount = fineAmount;
    }

    public int getAdminId() {
        return adminId;
    }

    public void setAdminId(int adminId) {
        this.adminId = adminId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Book getBook() {
        return book;
    }

    public void setBook(Book book) {
        this.book = book;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    // 判断是否逾期
    public boolean isOverdue() {
        return System.currentTimeMillis() > dueDate && "borrowed".equals(status);
    }

    // 判断是否可以续借
    public boolean canRenew() {
        return "borrowed".equals(status) && renewalCount < 2; // 最多续借2次
    }

    // 计算剩余天数
    public int getDaysRemaining() {
        if (!"borrowed".equals(status)) {
            return 0;
        }
        long diff = dueDate - System.currentTimeMillis();
        return (int) (diff / (24 * 60 * 60 * 1000));
    }

    @Override
    public String toString() {
        return "BorrowingRecord{" +
                "recordId=" + recordId +
                ", userId=" + userId +
                ", bookId=" + bookId +
                ", status='" + status + '\'' +
                ", renewalCount=" + renewalCount +
                ", daysRemaining=" + getDaysRemaining() +
                '}';
    }
}
