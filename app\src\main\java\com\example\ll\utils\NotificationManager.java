package com.example.ll.utils;

import android.Manifest;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.example.ll.R;
import com.example.ll.activity.UserMainActivity;

public class NotificationManager {
    
    private static final String CHANNEL_ID_GENERAL = "general";
    private static final String CHANNEL_ID_BORROWING = "borrowing";
    private static final String CHANNEL_ID_RESERVATION = "reservation";
    private static final String CHANNEL_ID_SYSTEM = "system";
    
    private static NotificationManager instance;
    private Context context;
    private android.app.NotificationManager notificationManager;
    
    private NotificationManager(Context context) {
        this.context = context.getApplicationContext();
        this.notificationManager = (android.app.NotificationManager) 
            context.getSystemService(Context.NOTIFICATION_SERVICE);
        createNotificationChannels();
    }
    
    public static synchronized NotificationManager getInstance(Context context) {
        if (instance == null) {
            instance = new NotificationManager(context);
        }
        return instance;
    }
    
    /**
     * 创建通知渠道
     */
    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 通用通知渠道
            NotificationChannel generalChannel = new NotificationChannel(
                CHANNEL_ID_GENERAL,
                "通用通知",
                android.app.NotificationManager.IMPORTANCE_DEFAULT
            );
            generalChannel.setDescription("应用的通用通知");
            
            // 借阅通知渠道
            NotificationChannel borrowingChannel = new NotificationChannel(
                CHANNEL_ID_BORROWING,
                "借阅通知",
                android.app.NotificationManager.IMPORTANCE_HIGH
            );
            borrowingChannel.setDescription("图书借阅相关通知");
            
            // 预约通知渠道
            NotificationChannel reservationChannel = new NotificationChannel(
                CHANNEL_ID_RESERVATION,
                "预约通知",
                android.app.NotificationManager.IMPORTANCE_HIGH
            );
            reservationChannel.setDescription("座位预约相关通知");
            
            // 系统通知渠道
            NotificationChannel systemChannel = new NotificationChannel(
                CHANNEL_ID_SYSTEM,
                "系统通知",
                android.app.NotificationManager.IMPORTANCE_LOW
            );
            systemChannel.setDescription("系统维护和更新通知");
            
            notificationManager.createNotificationChannel(generalChannel);
            notificationManager.createNotificationChannel(borrowingChannel);
            notificationManager.createNotificationChannel(reservationChannel);
            notificationManager.createNotificationChannel(systemChannel);
        }
    }
    
    /**
     * 显示借阅到期提醒
     */
    public void showBorrowingDueReminder(String bookTitle, String dueDate) {
        Intent intent = new Intent(context, UserMainActivity.class);
        intent.putExtra("tab_index", 2); // 跳转到借阅页面
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        Notification notification = new NotificationCompat.Builder(context, CHANNEL_ID_BORROWING)
            .setSmallIcon(R.drawable.ic_book)
            .setContentTitle("图书即将到期")
            .setContentText("《" + bookTitle + "》将于 " + dueDate + " 到期，请及时归还")
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build();
        
        safeNotify(1001, notification);
    }
    
    /**
     * 显示借阅逾期通知
     */
    public void showBorrowingOverdueNotification(String bookTitle, int overdueDays) {
        Intent intent = new Intent(context, UserMainActivity.class);
        intent.putExtra("tab_index", 2); // 跳转到借阅页面
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        Notification notification = new NotificationCompat.Builder(context, CHANNEL_ID_BORROWING)
            .setSmallIcon(R.drawable.ic_warning)
            .setContentTitle("图书已逾期")
            .setContentText("《" + bookTitle + "》已逾期 " + overdueDays + " 天，请尽快归还")
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build();
        
        safeNotify(1002, notification);
    }
    
    /**
     * 显示座位预约提醒
     */
    public void showSeatReservationReminder(String roomName, String timeSlot) {
        Intent intent = new Intent(context, UserMainActivity.class);
        intent.putExtra("tab_index", 3); // 跳转到座位页面
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        Notification notification = new NotificationCompat.Builder(context, CHANNEL_ID_RESERVATION)
            .setSmallIcon(R.drawable.ic_seat)
            .setContentTitle("座位预约提醒")
            .setContentText("您在 " + roomName + " 的座位预约（" + timeSlot + "）即将开始")
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build();
        
        safeNotify(2001, notification);
    }
    
    /**
     * 显示座位预约取消通知
     */
    public void showSeatReservationCancelled(String roomName, String timeSlot) {
        Notification notification = new NotificationCompat.Builder(context, CHANNEL_ID_RESERVATION)
            .setSmallIcon(R.drawable.ic_cancel)
            .setContentTitle("座位预约已取消")
            .setContentText("您在 " + roomName + " 的座位预约（" + timeSlot + "）已被取消")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build();
        
        safeNotify(2002, notification);
    }
    
    /**
     * 显示系统维护通知
     */
    public void showSystemMaintenanceNotification(String message) {
        Notification notification = new NotificationCompat.Builder(context, CHANNEL_ID_SYSTEM)
            .setSmallIcon(R.drawable.ic_settings)
            .setContentTitle("系统通知")
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setAutoCancel(true)
            .build();
        
        safeNotify(3001, notification);
    }
    
    /**
     * 显示新消息通知
     */
    public void showNewMessageNotification(String title, String content) {
        Intent intent = new Intent(context, UserMainActivity.class);
        intent.putExtra("tab_index", 0); // 跳转到首页
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        Notification notification = new NotificationCompat.Builder(context, CHANNEL_ID_GENERAL)
            .setSmallIcon(R.drawable.ic_message)
            .setContentTitle(title)
            .setContentText(content)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build();
        
        safeNotify(4001, notification);
    }
    
    /**
     * 显示账户安全通知
     */
    public void showSecurityNotification(String message) {
        Intent intent = new Intent(context, UserMainActivity.class);
        intent.putExtra("tab_index", 4); // 跳转到个人页面
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        Notification notification = new NotificationCompat.Builder(context, CHANNEL_ID_SYSTEM)
            .setSmallIcon(R.drawable.ic_security)
            .setContentTitle("账户安全")
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build();
        
        safeNotify(5001, notification);
    }
    
    /**
     * 清除所有通知
     */
    public void clearAllNotifications() {
        NotificationManagerCompat.from(context).cancelAll();
    }
    
    /**
     * 清除指定类型的通知
     */
    public void clearNotificationsByType(String type) {
        switch (type) {
            case "borrowing":
                NotificationManagerCompat.from(context).cancel(1001);
                NotificationManagerCompat.from(context).cancel(1002);
                break;
            case "reservation":
                NotificationManagerCompat.from(context).cancel(2001);
                NotificationManagerCompat.from(context).cancel(2002);
                break;
            case "system":
                NotificationManagerCompat.from(context).cancel(3001);
                break;
            case "message":
                NotificationManagerCompat.from(context).cancel(4001);
                break;
            case "security":
                NotificationManagerCompat.from(context).cancel(5001);
                break;
        }
    }
    
    /**
     * 检查通知权限
     */
    public boolean areNotificationsEnabled() {
        return NotificationManagerCompat.from(context).areNotificationsEnabled();
    }

    /**
     * 检查POST_NOTIFICATIONS权限（Android 13+）
     */
    public boolean hasPostNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ActivityCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS)
                == PackageManager.PERMISSION_GRANTED;
        }
        return true; // Android 13以下不需要此权限
    }

    /**
     * 安全发送通知（检查权限）
     */
    private void safeNotify(int notificationId, Notification notification) {
        if (hasPostNotificationPermission() && areNotificationsEnabled()) {
            NotificationManagerCompat.from(context).notify(notificationId, notification);
        }
    }
}
