<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/error_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 欢迎区域 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/error_color"
                android:layout_marginBottom="24dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_admin_shield"
                        app:tint="@android:color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp">

                        <TextView
                            android:id="@+id/tv_welcome"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="欢迎，超级管理员"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="图书馆管理系统"
                            android:textColor="@android:color/white"
                            android:textSize="14sp"
                            android:alpha="0.8"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 管理功能菜单 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="核心管理"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:padding="16dp" />

                    <LinearLayout
                        android:id="@+id/ll_user_management"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_person"
                            app:tint="@color/error_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="用户管理"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="16dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_hint" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider_color"
                        android:layout_marginStart="56dp" />

                    <LinearLayout
                        android:id="@+id/ll_book_management"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_book"
                            app:tint="@color/error_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="图书管理"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="16dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_hint" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider_color"
                        android:layout_marginStart="56dp" />

                    <LinearLayout
                        android:id="@+id/ll_borrowing_management"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_borrowing"
                            app:tint="@color/error_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="借阅管理"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="16dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_hint" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider_color"
                        android:layout_marginStart="56dp" />

                    <LinearLayout
                        android:id="@+id/ll_seat_management"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_seat"
                            app:tint="@color/error_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="座位管理"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="16dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_hint" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 系统功能 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="系统功能"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:padding="16dp" />

                    <LinearLayout
                        android:id="@+id/ll_data_statistics"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_chart"
                            app:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="数据统计"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="16dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_hint" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider_color"
                        android:layout_marginStart="56dp" />

                    <LinearLayout
                        android:id="@+id/ll_system_settings"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_settings"
                            app:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="系统设置"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="16dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_hint" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 退出登录 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:id="@+id/ll_logout"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="16dp"
                    android:background="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_logout"
                        app:tint="@color/error_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="退出登录"
                        android:textSize="16sp"
                        android:textColor="@color/error_color"
                        android:layout_marginStart="16dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
