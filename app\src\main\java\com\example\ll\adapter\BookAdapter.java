package com.example.ll.adapter;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.example.ll.R;
import com.example.ll.activity.BookDetailActivity;
import com.example.ll.model.Book;

import java.util.List;

public class BookAdapter extends RecyclerView.Adapter<BookAdapter.BookViewHolder> {

    private Context context;
    private List<Book> books;
    private boolean isHorizontal = false;

    public BookAdapter(Context context, List<Book> books) {
        this.context = context;
        this.books = books;
    }

    public BookAdapter(Context context, List<Book> books, boolean isHorizontal) {
        this.context = context;
        this.books = books;
        this.isHorizontal = isHorizontal;
    }

    @NonNull
    @Override
    public BookViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        int layoutId = isHorizontal ? R.layout.item_book_horizontal : R.layout.item_book_vertical;
        View view = LayoutInflater.from(context).inflate(layoutId, parent, false);
        return new BookViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull BookViewHolder holder, int position) {
        Book book = books.get(position);
        holder.bind(book);
    }

    @Override
    public int getItemCount() {
        return books.size();
    }

    public void updateBooks(List<Book> newBooks) {
        this.books.clear();
        this.books.addAll(newBooks);
        notifyDataSetChanged();
    }

    public class BookViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView ivCover;
        private TextView tvTitle, tvAuthor, tvAvailable;

        public BookViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_book);
            ivCover = itemView.findViewById(R.id.iv_book_cover);
            tvTitle = itemView.findViewById(R.id.tv_book_title);
            tvAuthor = itemView.findViewById(R.id.tv_book_author);
            tvAvailable = itemView.findViewById(R.id.tv_book_available);
        }

        public void bind(Book book) {
            tvTitle.setText(book.getTitle());
            tvAuthor.setText(book.getAuthor());
            tvAvailable.setText("可借：" + book.getAvailableCopies() + "本");

            // 加载图书封面
            if (book.getCoverImage() != null && !book.getCoverImage().isEmpty()) {
                Glide.with(context)
                        .load(book.getCoverImage())
                        .placeholder(R.drawable.ic_book_placeholder)
                        .error(R.drawable.ic_book_placeholder)
                        .into(ivCover);
            } else {
                ivCover.setImageResource(R.drawable.ic_book_placeholder);
            }

            // 设置可借状态颜色
            if (book.isAvailable()) {
                tvAvailable.setTextColor(context.getResources().getColor(R.color.success_color));
            } else {
                tvAvailable.setTextColor(context.getResources().getColor(R.color.error_color));
                tvAvailable.setText("暂无库存");
            }

            // 点击事件
            cardView.setOnClickListener(v -> {
                Intent intent = new Intent(context, BookDetailActivity.class);
                intent.putExtra("book_id", book.getBookId());
                context.startActivity(intent);
            });
        }
    }
}
