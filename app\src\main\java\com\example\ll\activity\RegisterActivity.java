package com.example.ll.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Patterns;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.ll.R;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.model.User;

public class RegisterActivity extends AppCompatActivity {

    private EditText etUsername, etPassword, etConfirmPassword, etEmail, etPhone, etNickname;
    private Button btnRegister;
    private Toolbar toolbar;

    private LibraryDatabase database;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_register);
        
        initViews();
        initData();
        setListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("用户注册");
        
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        etConfirmPassword = findViewById(R.id.et_confirm_password);
        etEmail = findViewById(R.id.et_email);
        etPhone = findViewById(R.id.et_phone);
        etNickname = findViewById(R.id.et_nickname);
        btnRegister = findViewById(R.id.btn_register);
    }

    private void initData() {
        database = LibraryDatabase.getInstance(this);
    }

    private void setListeners() {
        btnRegister.setOnClickListener(v -> performRegister());

        // 返回按钮 - 使用Toolbar的导航点击监听
        toolbar.setNavigationOnClickListener(v -> finish());
    }

    private void performRegister() {
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        String confirmPassword = etConfirmPassword.getText().toString().trim();
        String email = etEmail.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        String nickname = etNickname.getText().toString().trim();

        // 验证输入
        if (!validateInput(username, password, confirmPassword, email, phone)) {
            return;
        }

        // 临时简化注册逻辑，用于测试应用启动
        // TODO: 后续恢复完整的数据库注册逻辑

        Toast.makeText(this, "注册功能暂时简化，请使用演示账号登录：admin/123456", Toast.LENGTH_LONG).show();
        finish();
    }

    private boolean validateInput(String username, String password, String confirmPassword, 
                                String email, String phone) {
        
        if (TextUtils.isEmpty(username)) {
            etUsername.setError("请输入用户名");
            etUsername.requestFocus();
            return false;
        }
        
        if (username.length() < 3 || username.length() > 20) {
            etUsername.setError("用户名长度应为3-20个字符");
            etUsername.requestFocus();
            return false;
        }
        
        if (TextUtils.isEmpty(password)) {
            etPassword.setError("请输入密码");
            etPassword.requestFocus();
            return false;
        }
        
        if (password.length() < 6) {
            etPassword.setError("密码长度不能少于6位");
            etPassword.requestFocus();
            return false;
        }
        
        if (!password.equals(confirmPassword)) {
            etConfirmPassword.setError("两次输入的密码不一致");
            etConfirmPassword.requestFocus();
            return false;
        }
        
        if (!TextUtils.isEmpty(email) && !Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            etEmail.setError("邮箱格式不正确");
            etEmail.requestFocus();
            return false;
        }
        
        if (!TextUtils.isEmpty(phone) && !Patterns.PHONE.matcher(phone).matches()) {
            etPhone.setError("手机号格式不正确");
            etPhone.requestFocus();
            return false;
        }
        
        return true;
    }
}
