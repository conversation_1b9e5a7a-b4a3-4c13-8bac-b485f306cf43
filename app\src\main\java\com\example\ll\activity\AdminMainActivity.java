package com.example.ll.activity;

import android.content.Intent;
import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.ll.R;
import com.example.ll.utils.SharedPreferencesManager;

public class AdminMainActivity extends AppCompatActivity {

    private TextView tvWelcome;
    private LinearLayout llUserManagement, llBookManagement, llBorrowingManagement,
                      llSeatManagement, llSystemSettings, llDataStatistics, llLogout;

    private SharedPreferencesManager prefsManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_admin_main);

        initViews();
        initData();
        setListeners();
    }

    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setTitle("管理员控制台");

        tvWelcome = findViewById(R.id.tv_welcome);
        llUserManagement = findViewById(R.id.ll_user_management);
        llBookManagement = findViewById(R.id.ll_book_management);
        llBorrowingManagement = findViewById(R.id.ll_borrowing_management);
        llSeatManagement = findViewById(R.id.ll_seat_management);
        llSystemSettings = findViewById(R.id.ll_system_settings);
        llDataStatistics = findViewById(R.id.ll_data_statistics);
        llLogout = findViewById(R.id.ll_logout);
    }

    private void initData() {
        prefsManager = SharedPreferencesManager.getInstance(this);
        String adminName = prefsManager.getCurrentUserNickname();
        tvWelcome.setText("欢迎，" + adminName);
    }

    private void setListeners() {
        llUserManagement.setOnClickListener(v -> {
            Toast.makeText(this, "用户管理功能开发中", Toast.LENGTH_SHORT).show();
        });

        llBookManagement.setOnClickListener(v -> {
            Toast.makeText(this, "图书管理功能开发中", Toast.LENGTH_SHORT).show();
        });

        llBorrowingManagement.setOnClickListener(v -> {
            Toast.makeText(this, "借阅管理功能开发中", Toast.LENGTH_SHORT).show();
        });

        llSeatManagement.setOnClickListener(v -> {
            Toast.makeText(this, "座位管理功能开发中", Toast.LENGTH_SHORT).show();
        });

        llSystemSettings.setOnClickListener(v -> {
            Toast.makeText(this, "系统设置功能开发中", Toast.LENGTH_SHORT).show();
        });

        llDataStatistics.setOnClickListener(v -> {
            showStatistics();
        });

        llLogout.setOnClickListener(v -> {
            showLogoutDialog();
        });
    }

    private void showStatistics() {
        new AlertDialog.Builder(this)
                .setTitle("数据统计")
                .setMessage("总用户数：1,234\n总图书数：5,678\n当前借阅：234\n座位预约：89")
                .setPositiveButton("确定", null)
                .show();
    }

    private void showLogoutDialog() {
        new AlertDialog.Builder(this)
                .setTitle("退出登录")
                .setMessage("确定要退出管理员登录吗？")
                .setPositiveButton("确定", (dialog, which) -> {
                    logout();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void logout() {
        // 清除登录信息
        prefsManager.clearUserLogin();

        // 跳转到登录页面
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);

        finish();
    }

    @Override
    public void onBackPressed() {
        showLogoutDialog();
    }
}
