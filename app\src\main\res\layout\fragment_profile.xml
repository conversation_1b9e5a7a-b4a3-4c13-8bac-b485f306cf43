<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 用户信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary_color"
            android:orientation="vertical"
            android:padding="24dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_default_avatar"
                    app:civ_border_width="2dp"
                    app:civ_border_color="@android:color/white" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginStart="16dp">

                    <TextView
                        android:id="@+id/tv_nickname"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="用户昵称"
                        android:textColor="@android:color/white"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_username"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@username"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:alpha="0.8"
                        android:layout_marginTop="4dp" />

                    <TextView
                        android:id="@+id/tv_email"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:alpha="0.7"
                        android:layout_marginTop="2dp" />

                    <TextView
                        android:id="@+id/tv_phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="138****8888"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:alpha="0.7"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- 统计卡片区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-30dp"
            android:layout_marginHorizontal="16dp"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_borrowing_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="在借图书"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_reservation_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="座位预约"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_message_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/warning_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="消息通知"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- 功能菜单 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_personal_info"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="16dp"
                    android:background="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_person"
                        android:tint="@color/primary_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="个人信息"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="16dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_hint" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider_color"
                    android:layout_marginStart="56dp" />

                <LinearLayout
                    android:id="@+id/ll_change_password"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="16dp"
                    android:background="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_lock"
                        android:tint="@color/primary_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="修改密码"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="16dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_hint" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider_color"
                    android:layout_marginStart="56dp" />

                <LinearLayout
                    android:id="@+id/ll_settings"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="16dp"
                    android:background="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_settings"
                        android:tint="@color/primary_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="设置"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="16dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_hint" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider_color"
                    android:layout_marginStart="56dp" />

                <LinearLayout
                    android:id="@+id/ll_about"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="16dp"
                    android:background="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        android:tint="@color/primary_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="关于"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="16dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_hint" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 退出登录 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:id="@+id/ll_logout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?android:attr/selectableItemBackground">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_logout"
                    android:tint="@color/error_color" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="退出登录"
                    android:textSize="16sp"
                    android:textColor="@color/error_color"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
