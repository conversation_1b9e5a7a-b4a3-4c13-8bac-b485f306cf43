<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="编辑个人信息"
        app:titleTextColor="@color/white" />

    <!-- 内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 头像区域 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="头像"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <ImageView
                        android:id="@+id/iv_avatar"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/avatar_default"
                        android:scaleType="centerCrop"
                        android:background="@drawable/bg_circle"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackgroundBorderless" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="点击更换头像"
                        android:textSize="12sp"
                        android:textColor="@color/text_hint"
                        android:layout_marginTop="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 基本信息 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="基本信息"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="昵称">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_nickname"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLength="20" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="真实姓名">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_real_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName"
                            android:maxLength="10" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="性别"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <RadioGroup
                        android:id="@+id/rg_gender"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <RadioButton
                            android:id="@+id/rb_male"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="男"
                            android:layout_marginEnd="24dp" />

                        <RadioButton
                            android:id="@+id/rb_female"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="女"
                            android:layout_marginEnd="24dp" />

                        <RadioButton
                            android:id="@+id/rb_other"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="其他" />

                    </RadioGroup>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="生日"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_birthday"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:hint="点击选择生日"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:gravity="center_vertical"
                        android:background="?android:attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="12dp"
                        android:drawableEnd="@drawable/ic_calendar"
                        android:layout_marginBottom="12dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 联系信息 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="联系信息"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="邮箱">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="手机号">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone"
                            android:maxLength="11" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="学号">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_student_id"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLength="12" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="地址">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_address"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPostalAddress"
                            android:maxLength="100" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="个人简介">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_bio"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:lines="3"
                            android:maxLength="200"
                            android:gravity="top" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- 保存按钮 -->
    <Button
        android:id="@+id/btn_save"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="保存"
        android:textSize="16sp"
        android:textColor="@color/white"
        android:background="@color/primary_color"
        android:layout_margin="16dp"
        android:padding="16dp" />

</LinearLayout>
