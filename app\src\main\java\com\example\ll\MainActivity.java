package com.example.ll;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.example.ll.activity.LoginActivity;
import com.example.ll.activity.UserMainActivity;
import com.example.ll.database.LibraryDatabase;
import com.example.ll.utils.SharedPreferencesManager;

public class MainActivity extends AppCompatActivity {

    private static final int SPLASH_DELAY = 2000; // 2秒启动页

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // 初始化数据库
        LibraryDatabase.getInstance(this);

        // 延迟跳转
        new Handler().postDelayed(() -> {
            checkLoginStatus();
        }, SPLASH_DELAY);
    }

    private void checkLoginStatus() {
        SharedPreferencesManager prefsManager = SharedPreferencesManager.getInstance(this);

        if (prefsManager.isLoggedIn()) {
            // 已登录，跳转到主界面
            Intent intent = new Intent(this, UserMainActivity.class);
            startActivity(intent);
        } else {
            // 未登录，跳转到登录界面
            Intent intent = new Intent(this, LoginActivity.class);
            startActivity(intent);
        }

        finish();
    }
}