package com.example.ll.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.example.ll.R;
import com.example.ll.model.BorrowingRecord;
import com.example.ll.utils.DateUtils;

import java.util.List;

public class BorrowingRecordAdapter extends RecyclerView.Adapter<BorrowingRecordAdapter.BorrowingViewHolder> {

    private Context context;
    private List<BorrowingRecord> records;
    private OnRecordActionListener listener;

    public interface OnRecordActionListener {
        void onRenewBook(BorrowingRecord record);
        void onReturnBook(BorrowingRecord record);
    }

    public BorrowingRecordAdapter(Context context, List<BorrowingRecord> records) {
        this.context = context;
        this.records = records;
    }

    public void setOnRecordActionListener(OnRecordActionListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public BorrowingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_borrowing_record, parent, false);
        return new BorrowingViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull BorrowingViewHolder holder, int position) {
        BorrowingRecord record = records.get(position);
        holder.bind(record);
    }

    @Override
    public int getItemCount() {
        return records.size();
    }

    public class BorrowingViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView ivBookCover;
        private TextView tvBookTitle, tvBookAuthor, tvBorrowDate, tvDueDate, tvStatus, tvDaysRemaining;
        private Button btnRenew, btnReturn;

        public BorrowingViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_borrowing);
            ivBookCover = itemView.findViewById(R.id.iv_book_cover);
            tvBookTitle = itemView.findViewById(R.id.tv_book_title);
            tvBookAuthor = itemView.findViewById(R.id.tv_book_author);
            tvBorrowDate = itemView.findViewById(R.id.tv_borrow_date);
            tvDueDate = itemView.findViewById(R.id.tv_due_date);
            tvStatus = itemView.findViewById(R.id.tv_status);
            tvDaysRemaining = itemView.findViewById(R.id.tv_days_remaining);
            btnRenew = itemView.findViewById(R.id.btn_renew);
            btnReturn = itemView.findViewById(R.id.btn_return);
        }

        public void bind(BorrowingRecord record) {
            if (record.getBook() != null) {
                tvBookTitle.setText(record.getBook().getTitle());
                tvBookAuthor.setText(record.getBook().getAuthor());

                // 加载图书封面
                if (record.getBook().getCoverImage() != null && !record.getBook().getCoverImage().isEmpty()) {
                    Glide.with(context)
                            .load(record.getBook().getCoverImage())
                            .placeholder(R.drawable.ic_book_placeholder)
                            .error(R.drawable.ic_book_placeholder)
                            .into(ivBookCover);
                } else {
                    ivBookCover.setImageResource(R.drawable.ic_book_placeholder);
                }
            }

            // 设置日期信息
            tvBorrowDate.setText("借阅日期：" + DateUtils.formatDisplayDate(record.getBorrowDate()));
            tvDueDate.setText("应还日期：" + DateUtils.formatDisplayDate(record.getDueDate()));

            // 设置状态和剩余天数
            int daysRemaining = record.getDaysRemaining();
            if (record.isOverdue()) {
                tvStatus.setText("已逾期");
                tvStatus.setTextColor(context.getResources().getColor(R.color.error_color));
                tvDaysRemaining.setText("逾期 " + Math.abs(daysRemaining) + " 天");
                tvDaysRemaining.setTextColor(context.getResources().getColor(R.color.error_color));
            } else if (daysRemaining <= 3) {
                tvStatus.setText("即将到期");
                tvStatus.setTextColor(context.getResources().getColor(R.color.warning_color));
                tvDaysRemaining.setText("还剩 " + daysRemaining + " 天");
                tvDaysRemaining.setTextColor(context.getResources().getColor(R.color.warning_color));
            } else {
                tvStatus.setText("正常");
                tvStatus.setTextColor(context.getResources().getColor(R.color.success_color));
                tvDaysRemaining.setText("还剩 " + daysRemaining + " 天");
                tvDaysRemaining.setTextColor(context.getResources().getColor(R.color.text_secondary));
            }

            // 设置按钮状态
            if (record.canRenew()) {
                btnRenew.setEnabled(true);
                btnRenew.setText("续借 (" + record.getRenewalCount() + "/2)");
            } else {
                btnRenew.setEnabled(false);
                btnRenew.setText("不可续借");
            }

            // 设置点击事件
            btnRenew.setOnClickListener(v -> {
                if (listener != null && record.canRenew()) {
                    listener.onRenewBook(record);
                }
            });

            btnReturn.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onReturnBook(record);
                }
            });
        }
    }
}
