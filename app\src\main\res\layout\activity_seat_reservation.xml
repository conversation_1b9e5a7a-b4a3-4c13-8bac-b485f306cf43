<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="座位预约"
        app:titleTextColor="@color/white" />

    <!-- 内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 房间信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_room_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="自习室A"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tv_room_info"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="图书馆1楼 | 容量: 50人"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                    <TextView
                        android:id="@+id/tv_room_facilities"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="设施: 空调、WiFi、插座"
                        android:textSize="12sp"
                        android:textColor="@color/text_hint"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 日期选择 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="选择日期"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <HorizontalScrollView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/ll_date_selector"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal" />

                    </HorizontalScrollView>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 时间选择 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="选择时间"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="开始时间:"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary" />

                        <TextView
                            android:id="@+id/tv_start_time"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="09:00"
                            android:textSize="16sp"
                            android:textColor="@color/primary_color"
                            android:textStyle="bold"
                            android:layout_marginStart="8dp"
                            android:background="?android:attr/selectableItemBackground"
                            android:clickable="true"
                            android:focusable="true"
                            android:padding="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="结束时间:"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:layout_marginStart="16dp" />

                        <TextView
                            android:id="@+id/tv_end_time"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="12:00"
                            android:textSize="16sp"
                            android:textColor="@color/primary_color"
                            android:textStyle="bold"
                            android:layout_marginStart="8dp"
                            android:background="?android:attr/selectableItemBackground"
                            android:clickable="true"
                            android:focusable="true"
                            android:padding="8dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 座位选择 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="选择座位"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/tv_selected_seat"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="未选择"
                            android:textSize="14sp"
                            android:textColor="@color/text_hint" />

                    </LinearLayout>

                    <!-- 座位状态说明 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <View
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:background="@color/seat_available" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="可用"
                                android:textSize="10sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginStart="4dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <View
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:background="@color/seat_occupied" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="占用"
                                android:textSize="10sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginStart="4dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <View
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:background="@color/seat_selected" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="已选"
                                android:textSize="10sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginStart="4dp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- 座位网格 -->
                    <HorizontalScrollView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.example.ll.view.SeatGridView
                            android:id="@+id/seat_grid_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="8dp" />

                    </HorizontalScrollView>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 预约按钮 -->
            <Button
                android:id="@+id/btn_reserve"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="确认预约"
                android:textSize="16sp"
                android:textColor="@color/white"
                android:background="@color/primary_color"
                android:enabled="false"
                android:layout_marginTop="16dp"
                android:padding="16dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
