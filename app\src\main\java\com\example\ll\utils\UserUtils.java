package com.example.ll.utils;

import android.content.Context;
import android.text.TextUtils;

import com.example.ll.model.User;

import java.util.regex.Pattern;

public class UserUtils {
    
    // 用户状态常量
    public static final String STATUS_ACTIVE = "active";
    public static final String STATUS_FROZEN = "frozen";
    public static final String STATUS_DELETED = "deleted";
    
    // 用户角色常量
    public static final String ROLE_USER = "user";
    public static final String ROLE_ADMIN = "admin";
    public static final String ROLE_LIBRARIAN = "librarian";
    
    // 性别常量
    public static final String GENDER_MALE = "male";
    public static final String GENDER_FEMALE = "female";
    public static final String GENDER_OTHER = "other";
    
    // 验证正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^1[3-9]\\d{9}$"
    );
    
    private static final Pattern USERNAME_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_]{3,20}$"
    );
    
    private static final Pattern STUDENT_ID_PATTERN = Pattern.compile(
        "^\\d{8,12}$"
    );
    
    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        return !TextUtils.isEmpty(email) && EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        return !TextUtils.isEmpty(phone) && PHONE_PATTERN.matcher(phone).matches();
    }
    
    /**
     * 验证用户名格式
     */
    public static boolean isValidUsername(String username) {
        return !TextUtils.isEmpty(username) && USERNAME_PATTERN.matcher(username).matches();
    }
    
    /**
     * 验证学号格式
     */
    public static boolean isValidStudentId(String studentId) {
        return !TextUtils.isEmpty(studentId) && STUDENT_ID_PATTERN.matcher(studentId).matches();
    }
    
    /**
     * 检查用户是否为管理员
     */
    public static boolean isAdmin(User user) {
        return user != null && ROLE_ADMIN.equals(user.getRole());
    }
    
    /**
     * 检查用户是否为图书管理员
     */
    public static boolean isLibrarian(User user) {
        return user != null && ROLE_LIBRARIAN.equals(user.getRole());
    }
    
    /**
     * 检查用户是否有管理权限
     */
    public static boolean hasAdminPermission(User user) {
        return isAdmin(user) || isLibrarian(user);
    }
    
    /**
     * 检查用户状态是否正常
     */
    public static boolean isUserActive(User user) {
        return user != null && STATUS_ACTIVE.equals(user.getStatus());
    }
    
    /**
     * 检查用户是否被冻结
     */
    public static boolean isUserFrozen(User user) {
        return user != null && STATUS_FROZEN.equals(user.getStatus());
    }
    
    /**
     * 获取用户显示名称
     */
    public static String getDisplayName(User user) {
        if (user == null) {
            return "未知用户";
        }
        
        if (!TextUtils.isEmpty(user.getNickname())) {
            return user.getNickname();
        }
        
        if (!TextUtils.isEmpty(user.getRealName())) {
            return user.getRealName();
        }
        
        return user.getUsername();
    }
    
    /**
     * 获取用户角色显示文本
     */
    public static String getRoleDisplayText(String role) {
        switch (role) {
            case ROLE_ADMIN:
                return "管理员";
            case ROLE_LIBRARIAN:
                return "图书管理员";
            case ROLE_USER:
                return "普通用户";
            default:
                return "未知角色";
        }
    }
    
    /**
     * 获取用户状态显示文本
     */
    public static String getStatusDisplayText(String status) {
        switch (status) {
            case STATUS_ACTIVE:
                return "正常";
            case STATUS_FROZEN:
                return "已冻结";
            case STATUS_DELETED:
                return "已删除";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 获取性别显示文本
     */
    public static String getGenderDisplayText(String gender) {
        switch (gender) {
            case GENDER_MALE:
                return "男";
            case GENDER_FEMALE:
                return "女";
            case GENDER_OTHER:
                return "其他";
            default:
                return "未设置";
        }
    }
    
    /**
     * 验证用户注册信息
     */
    public static String validateUserRegistration(String username, String password, 
                                                 String email, String phone) {
        if (TextUtils.isEmpty(username)) {
            return "用户名不能为空";
        }
        
        if (!isValidUsername(username)) {
            return "用户名格式不正确（3-20位字母、数字或下划线）";
        }
        
        if (TextUtils.isEmpty(password)) {
            return "密码不能为空";
        }
        
        if (!PasswordUtils.isPasswordSecure(password)) {
            return "密码强度不够，请设置更安全的密码";
        }
        
        if (!TextUtils.isEmpty(email) && !isValidEmail(email)) {
            return "邮箱格式不正确";
        }
        
        if (!TextUtils.isEmpty(phone) && !isValidPhone(phone)) {
            return "手机号格式不正确";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 创建安全的用户对象（加密密码）
     */
    public static User createSecureUser(String username, String password, 
                                       String email, String phone) {
        User user = new User();
        user.setUsername(username);
        
        // 生成盐值并加密密码
        String salt = PasswordUtils.generateSalt();
        String hashedPassword = PasswordUtils.hashPassword(password, salt);
        
        user.setPassword(hashedPassword);
        user.setPasswordSalt(salt);
        user.setEmail(email);
        user.setPhone(phone);
        
        return user;
    }
    
    /**
     * 验证用户登录
     */
    public static boolean verifyUserLogin(User user, String password) {
        if (user == null || TextUtils.isEmpty(password)) {
            return false;
        }
        
        // 如果没有盐值，说明是旧版本的密码，直接比较
        if (TextUtils.isEmpty(user.getPasswordSalt())) {
            return password.equals(user.getPassword());
        }
        
        // 使用盐值验证密码
        return PasswordUtils.verifyPassword(password, user.getPassword(), user.getPasswordSalt());
    }
    
    /**
     * 更新用户密码（加密）
     */
    public static void updateUserPassword(User user, String newPassword) {
        String salt = PasswordUtils.generateSalt();
        String hashedPassword = PasswordUtils.hashPassword(newPassword, salt);
        
        user.setPassword(hashedPassword);
        user.setPasswordSalt(salt);
        user.setUpdatedAt(System.currentTimeMillis());
    }
    
    /**
     * 更新用户登录信息
     */
    public static void updateLoginInfo(User user, String loginIp) {
        user.setLastLoginTime(System.currentTimeMillis());
        user.setLastLoginIp(loginIp);
        user.setLoginCount(user.getLoginCount() + 1);
        user.setUpdatedAt(System.currentTimeMillis());
    }
    
    /**
     * 获取默认头像URL
     */
    public static String getDefaultAvatarUrl(String gender) {
        switch (gender) {
            case GENDER_MALE:
                return "android.resource://com.example.ll/drawable/avatar_male_default";
            case GENDER_FEMALE:
                return "android.resource://com.example.ll/drawable/avatar_female_default";
            default:
                return "android.resource://com.example.ll/drawable/avatar_default";
        }
    }
    
    /**
     * 格式化用户注册时间
     */
    public static String formatRegistrationTime(long timestamp) {
        return DateUtils.formatDisplayDateTime(timestamp);
    }

    /**
     * 格式化最后登录时间
     */
    public static String formatLastLoginTime(long timestamp) {
        if (timestamp == 0) {
            return "从未登录";
        }
        return DateUtils.formatDisplayDateTime(timestamp);
    }
}
